<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('asset_templates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('contract_type_id')->constrained()->onDelete('cascade'); // Thuộc loại hợp đồng nào
            $table->string('name'); // Tên template
            $table->text('description')->nullable(); // Mô tả template
            $table->text('preview_content')->nullable(); // Nội dung preview
            $table->string('template_file')->nullable(); // File template Word
            $table->boolean('is_active')->default(true); // Trạng thái hoạt động
            $table->boolean('is_default')->default(false); // Template mặc định
            $table->integer('sort_order')->default(0); // Thứ tự sắp xếp
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('asset_templates');
    }
};
