<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('documents', function (Blueprint $table) {
            $table->string('document_number')->unique()->after('id'); // Số hồ sơ
            $table->foreignId('contract_type_id')->nullable()->constrained()->after('document_number'); // Loại hợp đồng
            $table->foreignId('asset_template_id')->nullable()->constrained()->after('contract_type_id'); // Template sử dụng
            $table->foreignId('user_id')->constrained()->after('asset_template_id'); // Người tạo
            $table->string('title'); // Tiêu đ<PERSON> hồ sơ
            $table->text('description')->nullable(); // <PERSON>ô tả
            $table->enum('status', ['draft', 'processing', 'completed', 'cancelled'])->default('draft'); // Trạng thái
            $table->json('wizard_data')->nullable(); // Dữ liệu từ wizard form
            $table->string('generated_file')->nullable(); // File Word đã tạo
            $table->timestamp('completed_at')->nullable(); // Thời gian hoàn thành
            $table->text('notes')->nullable(); // Ghi chú
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('documents', function (Blueprint $table) {
            $table->dropForeign(['contract_type_id']);
            $table->dropForeign(['asset_template_id']);
            $table->dropForeign(['user_id']);
            $table->dropColumn([
                'document_number',
                'contract_type_id',
                'asset_template_id',
                'user_id',
                'title',
                'description',
                'status',
                'wizard_data',
                'generated_file',
                'completed_at',
                'notes'
            ]);
        });
    }
};
