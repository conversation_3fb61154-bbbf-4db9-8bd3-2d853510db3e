<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('asset_fields', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Tên field
            $table->string('label'); // Label hiển thị
            $table->enum('type', ['text', 'number', 'date', 'select', 'textarea', 'file', 'checkbox', 'radio']); // Loại field
            $table->json('options')->nullable(); // Options cho select, radio, checkbox
            $table->json('validation_rules')->nullable(); // Rules validation
            $table->text('placeholder')->nullable(); // Placeholder text
            $table->text('help_text')->nullable(); // Text hướng dẫn
            $table->boolean('is_required')->default(false); // B<PERSON>t buộc
            $table->boolean('is_active')->default(true); // Trạng thái hoạt động
            $table->integer('sort_order')->default(0); // Thứ tự sắp xếp
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('asset_fields');
    }
};
