<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('template_fields', function (Blueprint $table) {
            $table->id();
            $table->foreignId('asset_template_id')->constrained()->onDelete('cascade'); // Template ID
            $table->foreignId('asset_field_id')->constrained()->onDelete('cascade'); // Field ID
            $table->boolean('is_required')->default(false); // Override required từ field
            $table->integer('sort_order')->default(0); // Thứ tự trong template
            $table->string('group_name')->nullable(); // Nhóm field (VD: "Thông tin cơ bản", "Thông tin bổ sung")
            $table->json('custom_options')->nullable(); // Override options cho field này
            $table->timestamps();

            // Unique constraint để tránh duplicate field trong cùng template
            $table->unique(['asset_template_id', 'asset_field_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('template_fields');
    }
};
