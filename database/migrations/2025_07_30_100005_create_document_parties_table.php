<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('document_parties', function (Blueprint $table) {
            $table->id();
            $table->foreignId('document_id')->constrained()->onDelete('cascade'); // <PERSON><PERSON> sơ
            $table->enum('party_type', ['party_a', 'party_b', 'witness', 'other']); // Loại đương sự
            $table->string('full_name'); // Họ tên
            $table->year('birth_year'); // Năm sinh
            $table->string('id_number')->index(); // Số CCCD/Hộ chiếu
            $table->enum('id_type', ['cccd', 'passport', 'other'])->default('cccd'); // Loại giấy tờ
            $table->text('current_address'); // N<PERSON>i cư trú hiện tại
            $table->text('permanent_address')->nullable(); // <PERSON><PERSON> khẩu thường trú
            $table->string('phone')->nullable(); // Số điện thoại
            $table->string('email')->nullable(); // Email
            $table->enum('gender', ['male', 'female', 'other'])->nullable(); // Giới tính
            $table->string('occupation')->nullable(); // Nghề nghiệp
            $table->text('notes')->nullable(); // Ghi chú
            $table->integer('sort_order')->default(0); // Thứ tự
            $table->timestamps();

            // Index cho tìm kiếm
            $table->index(['full_name', 'id_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('document_parties');
    }
};
