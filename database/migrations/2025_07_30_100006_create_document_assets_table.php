<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('document_assets', function (Blueprint $table) {
            $table->id();
            $table->foreignId('document_id')->constrained()->onDelete('cascade'); // Hồ sơ
            $table->string('asset_name'); // Tên tài sản
            $table->string('asset_code')->nullable(); // Mã tài sản (từ QR)
            $table->json('field_values'); // Giá trị các field động theo template
            $table->text('description')->nullable(); // Mô tả tài sản
            $table->decimal('estimated_value', 15, 2)->nullable(); // Giá trị ước tính
            $table->string('currency', 3)->default('VND'); // Đơn vị tiền tệ
            $table->json('attachments')->nullable(); // File đính kèm
            $table->text('notes')->nullable(); // Ghi chú
            $table->integer('sort_order')->default(0); // Thứ tự
            $table->timestamps();

            // Index cho tìm kiếm
            $table->index(['asset_name', 'asset_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('document_assets');
    }
};
