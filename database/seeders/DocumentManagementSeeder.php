<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ContractType;
use App\Models\AssetField;
use App\Models\AssetTemplate;

class DocumentManagementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Contract Types
        $contractTypes = [
            [
                'name' => 'Hợp đồng mua bán nhà đất',
                'code' => 'HDMB_NHADAT',
                'description' => 'Hợp đồng mua bán bất động sản nhà đất',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Hợp đồng cho thuê',
                'code' => 'HDCHO_THUE',
                'description' => 'Hợp đồng cho thuê tài sản',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Hợp đồng vay tiền',
                'code' => 'HD_VAYTIEN',
                'description' => 'Hợp đồng vay tiền có thế chấp',
                'is_active' => true,
                'sort_order' => 3,
            ],
        ];

        foreach ($contractTypes as $contractType) {
            ContractType::firstOrCreate(['code' => $contractType['code']], $contractType);
        }

        // Create Asset Fields
        $assetFields = [
            [
                'name' => 'dia_chi',
                'label' => 'Địa chỉ',
                'type' => 'textarea',
                'placeholder' => 'Nhập địa chỉ đầy đủ',
                'is_required' => true,
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'dien_tich',
                'label' => 'Diện tích (m²)',
                'type' => 'number',
                'placeholder' => 'Nhập diện tích',
                'validation_rules' => ['numeric', 'min:0'],
                'is_required' => true,
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'so_to_ban_do',
                'label' => 'Số tờ bản đồ',
                'type' => 'text',
                'placeholder' => 'Nhập số tờ bản đồ',
                'is_required' => false,
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'so_thua',
                'label' => 'Số thửa',
                'type' => 'text',
                'placeholder' => 'Nhập số thửa',
                'is_required' => false,
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'loai_dat',
                'label' => 'Loại đất',
                'type' => 'select',
                'options' => [
                    'dat_o' => 'Đất ở',
                    'dat_thuong_mai' => 'Đất thương mại',
                    'dat_nong_nghiep' => 'Đất nông nghiệp',
                    'dat_cong_nghiep' => 'Đất công nghiệp',
                ],
                'is_required' => true,
                'is_active' => true,
                'sort_order' => 5,
            ],
            [
                'name' => 'gia_tri',
                'label' => 'Giá trị (VND)',
                'type' => 'number',
                'placeholder' => 'Nhập giá trị',
                'validation_rules' => ['numeric', 'min:0'],
                'is_required' => true,
                'is_active' => true,
                'sort_order' => 6,
            ],
            [
                'name' => 'so_giay_chung_nhan',
                'label' => 'Số giấy chứng nhận',
                'type' => 'text',
                'placeholder' => 'Nhập số giấy chứng nhận',
                'is_required' => false,
                'is_active' => true,
                'sort_order' => 7,
            ],
            [
                'name' => 'ngay_cap',
                'label' => 'Ngày cấp',
                'type' => 'date',
                'is_required' => false,
                'is_active' => true,
                'sort_order' => 8,
            ],
            [
                'name' => 'noi_cap',
                'label' => 'Nơi cấp',
                'type' => 'text',
                'placeholder' => 'Nhập nơi cấp',
                'is_required' => false,
                'is_active' => true,
                'sort_order' => 9,
            ],
            [
                'name' => 'hinh_anh',
                'label' => 'Hình ảnh',
                'type' => 'file',
                'help_text' => 'Upload hình ảnh tài sản',
                'is_required' => false,
                'is_active' => true,
                'sort_order' => 10,
            ],
        ];

        foreach ($assetFields as $field) {
            AssetField::firstOrCreate(['name' => $field['name']], $field);
        }

        // Create Asset Templates
        $nhadatContractType = ContractType::where('code', 'HDMB_NHADAT')->first();
        $chothueContractType = ContractType::where('code', 'HDCHO_THUE')->first();

        if ($nhadatContractType) {
            $nhadatTemplate = AssetTemplate::firstOrCreate([
                'contract_type_id' => $nhadatContractType->id,
                'name' => 'Template mua bán nhà đất cơ bản',
            ], [
                'description' => 'Template cơ bản cho hợp đồng mua bán nhà đất',
                'preview_content' => 'Template này bao gồm các thông tin cơ bản về nhà đất như địa chỉ, diện tích, giá trị...',
                'template_file' => 'template1.doc',
                'is_active' => true,
                'is_default' => true,
                'sort_order' => 1,
            ]);

            // Attach fields to template
            $basicFields = ['dia_chi', 'dien_tich', 'loai_dat', 'gia_tri'];
            $advancedFields = ['so_to_ban_do', 'so_thua', 'so_giay_chung_nhan', 'ngay_cap', 'noi_cap', 'hinh_anh'];

            foreach ($basicFields as $index => $fieldName) {
                $field = AssetField::where('name', $fieldName)->first();
                if ($field) {
                    $nhadatTemplate->assetFields()->syncWithoutDetaching([
                        $field->id => [
                            'is_required' => true,
                            'sort_order' => $index + 1,
                            'group_name' => 'Thông tin cơ bản',
                        ]
                    ]);
                }
            }

            foreach ($advancedFields as $index => $fieldName) {
                $field = AssetField::where('name', $fieldName)->first();
                if ($field) {
                    $nhadatTemplate->assetFields()->syncWithoutDetaching([
                        $field->id => [
                            'is_required' => false,
                            'sort_order' => count($basicFields) + $index + 1,
                            'group_name' => 'Thông tin bổ sung',
                        ]
                    ]);
                }
            }
        }

        if ($chothueContractType) {
            $chothueTemplate = AssetTemplate::firstOrCreate([
                'contract_type_id' => $chothueContractType->id,
                'name' => 'Template cho thuê cơ bản',
            ], [
                'description' => 'Template cơ bản cho hợp đồng cho thuê',
                'preview_content' => 'Template này bao gồm các thông tin cơ bản về tài sản cho thuê...',
                'template_file' => 'template1.doc',
                'is_active' => true,
                'is_default' => true,
                'sort_order' => 1,
            ]);

            // Attach basic fields for rental template
            $rentalFields = ['dia_chi', 'dien_tich', 'loai_dat', 'hinh_anh'];
            foreach ($rentalFields as $index => $fieldName) {
                $field = AssetField::where('name', $fieldName)->first();
                if ($field) {
                    $chothueTemplate->assetFields()->syncWithoutDetaching([
                        $field->id => [
                            'is_required' => $fieldName !== 'hinh_anh',
                            'sort_order' => $index + 1,
                            'group_name' => 'Thông tin tài sản',
                        ]
                    ]);
                }
            }
        }
    }
}
