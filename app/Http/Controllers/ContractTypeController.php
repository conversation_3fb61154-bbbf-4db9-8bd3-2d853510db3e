<?php

namespace App\Http\Controllers;

use App\Models\ContractType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Yajra\DataTables\Facades\DataTables;

class ContractTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Check permission
        if (!Auth::user()->hasPermission('contract-types.view')) {
            abort(403, 'Bạn không có quyền xem danh sách loại hợp đồng');
        }

        if ($request->ajax()) {
            $query = ContractType::query();

            return DataTables::of($query)
                ->addColumn('action', function ($contractType) {
                    $actions = '';
                    
                    if (Auth::user()->hasPermission('contract-types.edit')) {
                        $actions .= '<button type="button" class="btn btn-sm btn-outline-warning me-1" onclick="editContractType(' . $contractType->id . ')">
                            <i class="ri-edit-line"></i>
                        </button>';
                    }

                    if (Auth::user()->hasPermission('contract-types.delete')) {
                        $actions .= '<button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteContractType(' . $contractType->id . ')">
                            <i class="ri-delete-bin-line"></i>
                        </button>';
                    }

                    return $actions;
                })
                ->addColumn('status_badge', function ($contractType) {
                    $class = $contractType->is_active ? 'bg-label-success' : 'bg-label-secondary';
                    $text = $contractType->is_active ? 'Hoạt động' : 'Không hoạt động';
                    return '<span class="badge ' . $class . '">' . $text . '</span>';
                })
                ->addColumn('templates_count', function ($contractType) {
                    return $contractType->assetTemplates()->count();
                })
                ->rawColumns(['action', 'status_badge'])
                ->make(true);
        }

        return view('contract-types.index');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Check permission
        if (!Auth::user()->hasPermission('contract-types.create')) {
            abort(403, 'Bạn không có quyền tạo loại hợp đồng');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:contract_types,code',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        try {
            $contractType = ContractType::create($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Loại hợp đồng đã được tạo thành công',
                'data' => $contractType,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tạo loại hợp đồng: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(ContractType $contractType)
    {
        // Check permission
        if (!Auth::user()->hasPermission('contract-types.view')) {
            abort(403, 'Bạn không có quyền xem loại hợp đồng');
        }

        return response()->json($contractType);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ContractType $contractType)
    {
        // Check permission
        if (!Auth::user()->hasPermission('contract-types.edit')) {
            abort(403, 'Bạn không có quyền sửa loại hợp đồng');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:contract_types,code,' . $contractType->id,
            'description' => 'nullable|string',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        try {
            $contractType->update($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Loại hợp đồng đã được cập nhật thành công',
                'data' => $contractType,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật loại hợp đồng: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ContractType $contractType)
    {
        // Check permission
        if (!Auth::user()->hasPermission('contract-types.delete')) {
            abort(403, 'Bạn không có quyền xóa loại hợp đồng');
        }

        // Check if contract type has associated documents
        if ($contractType->documents()->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể xóa loại hợp đồng này vì đã có hồ sơ sử dụng',
            ], 400);
        }

        try {
            $contractType->delete();

            return response()->json([
                'success' => true,
                'message' => 'Loại hợp đồng đã được xóa thành công',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xóa loại hợp đồng: ' . $e->getMessage(),
            ], 500);
        }
    }
}
