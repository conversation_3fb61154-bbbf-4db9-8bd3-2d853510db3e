<?php

namespace App\Http\Controllers;

use App\Models\AssetField;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Yajra\DataTables\Facades\DataTables;

class AssetFieldController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Check permission
        if (!Auth::user()->hasPermission('asset-fields.view')) {
            abort(403, 'Bạn không có quyền xem danh sách field động');
        }

        if ($request->ajax()) {
            // Check if simple format is requested (for template management)
            if ($request->get('format') === 'simple') {
                $fields = AssetField::active()->ordered()->get();
                return response()->json($fields);
            }

            $query = AssetField::query();

            return DataTables::of($query)
                ->addColumn('action', function ($field) {
                    $actions = '';

                    if (Auth::user()->hasPermission('asset-fields.edit')) {
                        $actions .= '<button type="button" class="btn btn-sm btn-outline-warning me-1" onclick="editAssetField(' . $field->id . ')">
                            <i class="ri-edit-line"></i>
                        </button>';
                    }

                    if (Auth::user()->hasPermission('asset-fields.delete')) {
                        $actions .= '<button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteAssetField(' . $field->id . ')">
                            <i class="ri-delete-bin-line"></i>
                        </button>';
                    }

                    return $actions;
                })
                ->addColumn('type_formatted', function ($field) {
                    $types = AssetField::getFieldTypes();
                    return $types[$field->type] ?? $field->type;
                })
                ->addColumn('status_badge', function ($field) {
                    $class = $field->is_active ? 'bg-label-success' : 'bg-label-secondary';
                    $text = $field->is_active ? 'Hoạt động' : 'Không hoạt động';
                    return '<span class="badge ' . $class . '">' . $text . '</span>';
                })
                ->addColumn('required_badge', function ($field) {
                    $class = $field->is_required ? 'bg-label-warning' : 'bg-label-secondary';
                    $text = $field->is_required ? 'Bắt buộc' : 'Không bắt buộc';
                    return '<span class="badge ' . $class . '">' . $text . '</span>';
                })
                ->addColumn('templates_count', function ($field) {
                    return $field->assetTemplates()->count();
                })
                ->rawColumns(['action', 'status_badge', 'required_badge'])
                ->make(true);
        }

        $fieldTypes = AssetField::getFieldTypes();
        return view('asset-fields.index', compact('fieldTypes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Check permission
        if (!Auth::user()->hasPermission('asset-fields.create')) {
            abort(403, 'Bạn không có quyền tạo field động');
        }

        $request->validate([
            'name' => 'required|string|max:255|unique:asset_fields,name',
            'label' => 'required|string|max:255',
            'type' => 'required|in:text,number,date,select,textarea,file,checkbox,radio',
            'options' => 'nullable|array',
            'validation_rules' => 'nullable|array',
            'placeholder' => 'nullable|string',
            'help_text' => 'nullable|string',
            'is_required' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        try {
            $data = $request->all();
            
            // Convert options to proper format if provided
            if ($request->has('options') && is_array($request->options)) {
                $options = [];
                foreach ($request->options as $option) {
                    if (isset($option['key']) && isset($option['value'])) {
                        $options[$option['key']] = $option['value'];
                    }
                }
                $data['options'] = $options;
            }

            $field = AssetField::create($data);

            return response()->json([
                'success' => true,
                'message' => 'Field động đã được tạo thành công',
                'data' => $field,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tạo field động: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(AssetField $assetField)
    {
        // Check permission
        if (!Auth::user()->hasPermission('asset-fields.view')) {
            abort(403, 'Bạn không có quyền xem field động');
        }

        return response()->json($assetField);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, AssetField $assetField)
    {
        // Check permission
        if (!Auth::user()->hasPermission('asset-fields.edit')) {
            abort(403, 'Bạn không có quyền sửa field động');
        }

        $request->validate([
            'name' => 'required|string|max:255|unique:asset_fields,name,' . $assetField->id,
            'label' => 'required|string|max:255',
            'type' => 'required|in:text,number,date,select,textarea,file,checkbox,radio',
            'options' => 'nullable|array',
            'validation_rules' => 'nullable|array',
            'placeholder' => 'nullable|string',
            'help_text' => 'nullable|string',
            'is_required' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        try {
            $data = $request->all();
            
            // Convert options to proper format if provided
            if ($request->has('options') && is_array($request->options)) {
                $options = [];
                foreach ($request->options as $option) {
                    if (isset($option['key']) && isset($option['value'])) {
                        $options[$option['key']] = $option['value'];
                    }
                }
                $data['options'] = $options;
            }

            $assetField->update($data);

            return response()->json([
                'success' => true,
                'message' => 'Field động đã được cập nhật thành công',
                'data' => $assetField,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật field động: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AssetField $assetField)
    {
        // Check permission
        if (!Auth::user()->hasPermission('asset-fields.delete')) {
            abort(403, 'Bạn không có quyền xóa field động');
        }

        // Check if field is used in any templates
        if ($assetField->assetTemplates()->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể xóa field này vì đã được sử dụng trong template',
            ], 400);
        }

        try {
            $assetField->delete();

            return response()->json([
                'success' => true,
                'message' => 'Field động đã được xóa thành công',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xóa field động: ' . $e->getMessage(),
            ], 500);
        }
    }
}
