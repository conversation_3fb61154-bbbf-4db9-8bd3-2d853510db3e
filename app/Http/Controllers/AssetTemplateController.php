<?php

namespace App\Http\Controllers;

use App\Models\AssetTemplate;
use App\Models\ContractType;
use App\Models\AssetField;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class AssetTemplateController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Check permission
        if (!Auth::user()->hasPermission('asset-templates.view')) {
            abort(403, 'Bạn không có quyền xem danh sách template');
        }

        if ($request->ajax()) {
            $query = AssetTemplate::with('contractType');

            return DataTables::of($query)
                ->addColumn('action', function ($template) {
                    $actions = '';
                    
                    if (Auth::user()->hasPermission('asset-templates.view')) {
                        $actions .= '<button type="button" class="btn btn-sm btn-outline-info me-1" onclick="viewTemplate(' . $template->id . ')">
                            <i class="ri-eye-line"></i>
                        </button>';
                    }

                    if (Auth::user()->hasPermission('asset-templates.edit')) {
                        $actions .= '<button type="button" class="btn btn-sm btn-outline-warning me-1" onclick="editTemplate(' . $template->id . ')">
                            <i class="ri-edit-line"></i>
                        </button>';
                    }

                    if (Auth::user()->hasPermission('asset-templates.delete')) {
                        $actions .= '<button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteTemplate(' . $template->id . ')">
                            <i class="ri-delete-bin-line"></i>
                        </button>';
                    }

                    return $actions;
                })
                ->addColumn('contract_type_name', function ($template) {
                    return $template->contractType ? $template->contractType->name : '-';
                })
                ->addColumn('status_badge', function ($template) {
                    $class = $template->is_active ? 'bg-label-success' : 'bg-label-secondary';
                    $text = $template->is_active ? 'Hoạt động' : 'Không hoạt động';
                    return '<span class="badge ' . $class . '">' . $text . '</span>';
                })
                ->addColumn('default_badge', function ($template) {
                    if ($template->is_default) {
                        return '<span class="badge bg-label-primary">Mặc định</span>';
                    }
                    return '';
                })
                ->addColumn('fields_count', function ($template) {
                    return $template->assetFields()->count();
                })
                ->addColumn('documents_count', function ($template) {
                    return $template->documents()->count();
                })
                ->rawColumns(['action', 'status_badge', 'default_badge'])
                ->make(true);
        }

        $contractTypes = ContractType::active()->ordered()->get();
        return view('asset-templates.index', compact('contractTypes'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Check permission
        if (!Auth::user()->hasPermission('asset-templates.create')) {
            abort(403, 'Bạn không có quyền tạo template');
        }

        $contractTypes = ContractType::active()->ordered()->get();
        $assetFields = AssetField::active()->ordered()->get();
        
        return view('asset-templates.create', compact('contractTypes', 'assetFields'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Check permission
        if (!Auth::user()->hasPermission('asset-templates.create')) {
            abort(403, 'Bạn không có quyền tạo template');
        }

        $request->validate([
            'contract_type_id' => 'required|exists:contract_types,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'preview_content' => 'nullable|string',
            'template_file' => 'nullable|string|max:255',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
            'sort_order' => 'integer|min:0',
            'fields' => 'nullable|array',
            'fields.*.field_id' => 'required|exists:asset_fields,id',
            'fields.*.is_required' => 'boolean',
            'fields.*.sort_order' => 'integer|min:0',
            'fields.*.group_name' => 'nullable|string|max:255',
        ]);

        try {
            DB::beginTransaction();

            $template = AssetTemplate::create($request->only([
                'contract_type_id', 'name', 'description', 'preview_content',
                'template_file', 'is_active', 'is_default', 'sort_order'
            ]));

            // Set as default if requested
            if ($request->is_default) {
                $template->setAsDefault();
            }

            // Attach fields
            if ($request->has('fields')) {
                foreach ($request->fields as $fieldData) {
                    $template->assetFields()->attach($fieldData['field_id'], [
                        'is_required' => $fieldData['is_required'] ?? false,
                        'sort_order' => $fieldData['sort_order'] ?? 0,
                        'group_name' => $fieldData['group_name'] ?? 'Thông tin cơ bản',
                        'custom_options' => $fieldData['custom_options'] ?? null,
                    ]);
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Template đã được tạo thành công',
                'data' => $template,
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi tạo template: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(AssetTemplate $assetTemplate)
    {
        // Check permission
        if (!Auth::user()->hasPermission('asset-templates.view')) {
            abort(403, 'Bạn không có quyền xem template');
        }

        $assetTemplate->load(['contractType', 'assetFields']);
        $groupedFields = $assetTemplate->getGroupedFields();

        return response()->json([
            'template' => $assetTemplate,
            'grouped_fields' => $groupedFields,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(AssetTemplate $assetTemplate)
    {
        // Check permission
        if (!Auth::user()->hasPermission('asset-templates.edit')) {
            abort(403, 'Bạn không có quyền sửa template');
        }

        $assetTemplate->load(['contractType', 'assetFields']);
        $contractTypes = ContractType::active()->ordered()->get();
        $assetFields = AssetField::active()->ordered()->get();
        
        return view('asset-templates.edit', compact('assetTemplate', 'contractTypes', 'assetFields'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, AssetTemplate $assetTemplate)
    {
        // Check permission
        if (!Auth::user()->hasPermission('asset-templates.edit')) {
            abort(403, 'Bạn không có quyền sửa template');
        }

        $request->validate([
            'contract_type_id' => 'required|exists:contract_types,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'preview_content' => 'nullable|string',
            'template_file' => 'nullable|string|max:255',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
            'sort_order' => 'integer|min:0',
            'fields' => 'nullable|array',
            'fields.*.field_id' => 'required|exists:asset_fields,id',
            'fields.*.is_required' => 'boolean',
            'fields.*.sort_order' => 'integer|min:0',
            'fields.*.group_name' => 'nullable|string|max:255',
        ]);

        try {
            DB::beginTransaction();

            $assetTemplate->update($request->only([
                'contract_type_id', 'name', 'description', 'preview_content',
                'template_file', 'is_active', 'is_default', 'sort_order'
            ]));

            // Set as default if requested
            if ($request->is_default) {
                $assetTemplate->setAsDefault();
            }

            // Sync fields
            $fieldsData = [];
            if ($request->has('fields')) {
                foreach ($request->fields as $fieldData) {
                    $fieldsData[$fieldData['field_id']] = [
                        'is_required' => $fieldData['is_required'] ?? false,
                        'sort_order' => $fieldData['sort_order'] ?? 0,
                        'group_name' => $fieldData['group_name'] ?? 'Thông tin cơ bản',
                        'custom_options' => $fieldData['custom_options'] ?? null,
                    ];
                }
            }
            $assetTemplate->assetFields()->sync($fieldsData);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Template đã được cập nhật thành công',
                'data' => $assetTemplate,
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi cập nhật template: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(AssetTemplate $assetTemplate)
    {
        // Check permission
        if (!Auth::user()->hasPermission('asset-templates.delete')) {
            abort(403, 'Bạn không có quyền xóa template');
        }

        // Check if template is used in any documents
        if ($assetTemplate->documents()->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể xóa template này vì đã có hồ sơ sử dụng',
            ], 400);
        }

        try {
            $assetTemplate->delete();

            return response()->json([
                'success' => true,
                'message' => 'Template đã được xóa thành công',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra khi xóa template: ' . $e->getMessage(),
            ], 500);
        }
    }
}
