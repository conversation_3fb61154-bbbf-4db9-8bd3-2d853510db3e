<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AssetTemplate extends Model
{
    protected $fillable = [
        'contract_type_id',
        'name',
        'description',
        'preview_content',
        'template_file',
        'is_active',
        'is_default',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_default' => 'boolean',
    ];

    /**
     * Get the contract type that owns this template
     */
    public function contractType(): BelongsTo
    {
        return $this->belongsTo(ContractType::class);
    }

    /**
     * Get the fields for this template
     */
    public function assetFields(): BelongsToMany
    {
        return $this->belongsToMany(AssetField::class, 'template_fields')
            ->withPivot(['is_required', 'sort_order', 'group_name', 'custom_options'])
            ->withTimestamps()
            ->orderBy('template_fields.sort_order');
    }

    /**
     * Get the active fields for this template
     */
    public function activeAssetFields(): BelongsToMany
    {
        return $this->assetFields()->where('asset_fields.is_active', true);
    }

    /**
     * Get the documents using this template
     */
    public function documents(): HasMany
    {
        return $this->hasMany(Document::class);
    }

    /**
     * Scope for active templates
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered templates
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get fields grouped by group_name
     */
    public function getGroupedFields(): array
    {
        $fields = $this->activeAssetFields()->get();
        $grouped = [];

        foreach ($fields as $field) {
            $groupName = $field->pivot->group_name ?? 'Thông tin cơ bản';
            $grouped[$groupName][] = $field;
        }

        return $grouped;
    }

    /**
     * Set as default template for its contract type
     */
    public function setAsDefault(): void
    {
        // Remove default from other templates of same contract type
        static::where('contract_type_id', $this->contract_type_id)
            ->where('id', '!=', $this->id)
            ->update(['is_default' => false]);

        // Set this as default
        $this->update(['is_default' => true]);
    }
}
