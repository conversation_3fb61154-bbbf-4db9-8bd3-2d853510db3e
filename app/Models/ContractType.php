<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ContractType extends Model
{
    protected $fillable = [
        'name',
        'code',
        'description',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the asset templates for this contract type
     */
    public function assetTemplates(): HasMany
    {
        return $this->hasMany(AssetTemplate::class);
    }

    /**
     * Get the active asset templates for this contract type
     */
    public function activeAssetTemplates(): HasMany
    {
        return $this->hasMany(AssetTemplate::class)->where('is_active', true);
    }

    /**
     * Get the documents for this contract type
     */
    public function documents(): HasMany
    {
        return $this->hasMany(Document::class);
    }

    /**
     * Scope for active contract types
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered contract types
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get the default template for this contract type
     */
    public function getDefaultTemplate()
    {
        return $this->assetTemplates()->where('is_default', true)->first();
    }
}
