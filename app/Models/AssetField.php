<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class AssetField extends Model
{
    protected $fillable = [
        'name',
        'label',
        'type',
        'options',
        'validation_rules',
        'placeholder',
        'help_text',
        'is_required',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'options' => 'array',
        'validation_rules' => 'array',
        'is_required' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get the asset templates that use this field
     */
    public function assetTemplates(): BelongsToMany
    {
        return $this->belongsToMany(AssetTemplate::class, 'template_fields')
            ->withPivot(['is_required', 'sort_order', 'group_name', 'custom_options'])
            ->withTimestamps();
    }

    /**
     * Scope for active fields
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for ordered fields
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('label');
    }

    /**
     * Scope for ordered fields
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('label');
    }

    /**
     * Get field type options
     */
    public static function getFieldTypes(): array
    {
        return [
            'text' => 'Text',
            'number' => 'Number',
            'date' => 'Date',
            'select' => 'Select',
            'textarea' => 'Textarea',
            'file' => 'File Upload',
            'checkbox' => 'Checkbox',
            'radio' => 'Radio Button',
        ];
    }

    /**
     * Get validation rules for this field
     */
    public function getValidationRules(): array
    {
        $rules = $this->validation_rules ?? [];
        
        if ($this->is_required) {
            $rules[] = 'required';
        }

        return $rules;
    }
}
