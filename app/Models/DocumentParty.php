<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DocumentParty extends Model
{
    protected $fillable = [
        'document_id',
        'party_type',
        'full_name',
        'birth_year',
        'id_number',
        'id_type',
        'current_address',
        'permanent_address',
        'phone',
        'email',
        'gender',
        'occupation',
        'notes',
        'sort_order',
    ];

    /**
     * Get the document that owns this party
     */
    public function document(): BelongsTo
    {
        return $this->belongsTo(Document::class);
    }

    /**
     * Get party type options
     */
    public static function getPartyTypes(): array
    {
        return [
            'party_a' => 'Bên A',
            'party_b' => 'Bên B',
            'witness' => 'Người chứng kiến',
            'other' => 'Khác',
        ];
    }

    /**
     * Get ID type options
     */
    public static function getIdTypes(): array
    {
        return [
            'cccd' => 'CCCD',
            'passport' => 'Hộ chiếu',
            'other' => 'Khác',
        ];
    }

    /**
     * Get gender options
     */
    public static function getGenderOptions(): array
    {
        return [
            'male' => 'Nam',
            'female' => 'Nữ',
            'other' => 'Khác',
        ];
    }

    /**
     * Scope for searching by name or ID number
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('full_name', 'like', "%{$search}%")
              ->orWhere('id_number', 'like', "%{$search}%");
        });
    }

    /**
     * Scope for specific party type
     */
    public function scopePartyType($query, $type)
    {
        return $query->where('party_type', $type);
    }

    /**
     * Get formatted party type
     */
    public function getFormattedPartyTypeAttribute(): string
    {
        return static::getPartyTypes()[$this->party_type] ?? $this->party_type;
    }

    /**
     * Get formatted ID type
     */
    public function getFormattedIdTypeAttribute(): string
    {
        return static::getIdTypes()[$this->id_type] ?? $this->id_type;
    }

    /**
     * Get age from birth year
     */
    public function getAgeAttribute(): int
    {
        return now()->year - $this->birth_year;
    }
}
