<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DocumentAsset extends Model
{
    protected $fillable = [
        'document_id',
        'asset_name',
        'asset_code',
        'field_values',
        'description',
        'estimated_value',
        'currency',
        'attachments',
        'notes',
        'sort_order',
    ];

    protected $casts = [
        'field_values' => 'array',
        'attachments' => 'array',
        'estimated_value' => 'decimal:2',
    ];

    /**
     * Get the document that owns this asset
     */
    public function document(): BelongsTo
    {
        return $this->belongsTo(Document::class);
    }

    /**
     * Get currency options
     */
    public static function getCurrencyOptions(): array
    {
        return [
            'VND' => 'VND',
            'USD' => 'USD',
            'EUR' => 'EUR',
        ];
    }

    /**
     * Scope for searching by name or code
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('asset_name', 'like', "%{$search}%")
              ->orWhere('asset_code', 'like', "%{$search}%");
        });
    }

    /**
     * Get field value by field name
     */
    public function getFieldValue(string $fieldName, $default = null)
    {
        return $this->field_values[$fieldName] ?? $default;
    }

    /**
     * Set field value
     */
    public function setFieldValue(string $fieldName, $value): void
    {
        $fieldValues = $this->field_values ?? [];
        $fieldValues[$fieldName] = $value;
        $this->field_values = $fieldValues;
    }

    /**
     * Get formatted estimated value
     */
    public function getFormattedEstimatedValueAttribute(): string
    {
        if (!$this->estimated_value) {
            return 'Chưa định giá';
        }

        return number_format($this->estimated_value, 0, ',', '.') . ' ' . $this->currency;
    }

    /**
     * Get attachment URLs
     */
    public function getAttachmentUrls(): array
    {
        if (!$this->attachments) {
            return [];
        }

        return array_map(function ($attachment) {
            return asset('storage/' . $attachment);
        }, $this->attachments);
    }

    /**
     * Add attachment
     */
    public function addAttachment(string $filePath): void
    {
        $attachments = $this->attachments ?? [];
        $attachments[] = $filePath;
        $this->attachments = $attachments;
    }

    /**
     * Remove attachment
     */
    public function removeAttachment(string $filePath): void
    {
        $attachments = $this->attachments ?? [];
        $attachments = array_filter($attachments, fn($attachment) => $attachment !== $filePath);
        $this->attachments = array_values($attachments);
    }
}
