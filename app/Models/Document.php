<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Document extends Model
{
    protected $fillable = [
        'document_number',
        'contract_type_id',
        'asset_template_id',
        'user_id',
        'title',
        'description',
        'status',
        'wizard_data',
        'generated_file',
        'completed_at',
        'notes',
    ];

    protected $casts = [
        'wizard_data' => 'array',
        'completed_at' => 'datetime',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($document) {
            if (!$document->document_number) {
                $document->document_number = static::generateDocumentNumber();
            }
        });
    }

    /**
     * Get the contract type that owns this document
     */
    public function contractType(): BelongsTo
    {
        return $this->belongsTo(ContractType::class);
    }

    /**
     * Get the asset template that owns this document
     */
    public function assetTemplate(): BelongsTo
    {
        return $this->belongsTo(AssetTemplate::class);
    }

    /**
     * Get the user that owns this document
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the parties for this document
     */
    public function parties(): HasMany
    {
        return $this->hasMany(DocumentParty::class)->orderBy('sort_order');
    }

    /**
     * Get the assets for this document
     */
    public function assets(): HasMany
    {
        return $this->hasMany(DocumentAsset::class)->orderBy('sort_order');
    }

    /**
     * Get status options
     */
    public static function getStatusOptions(): array
    {
        return [
            'draft' => 'Nháp',
            'processing' => 'Đang xử lý',
            'completed' => 'Hoàn thành',
            'cancelled' => 'Đã hủy',
        ];
    }

    /**
     * Generate unique document number
     */
    public static function generateDocumentNumber(): string
    {
        $prefix = 'DOC';
        $date = now()->format('Ymd');

        do {
            $random = strtoupper(Str::random(4));
            $documentNumber = "{$prefix}{$date}{$random}";
        } while (static::where('document_number', $documentNumber)->exists());

        return $documentNumber;
    }

    /**
     * Scope for status
     */
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for search
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('document_number', 'like', "%{$search}%")
              ->orWhere('title', 'like', "%{$search}%")
              ->orWhereHas('parties', function ($partyQuery) use ($search) {
                  $partyQuery->where('full_name', 'like', "%{$search}%")
                           ->orWhere('id_number', 'like', "%{$search}%");
              });
        });
    }

    /**
     * Get formatted status
     */
    public function getFormattedStatusAttribute(): string
    {
        return static::getStatusOptions()[$this->status] ?? $this->status;
    }

    /**
     * Check if document is editable
     */
    public function isEditable(): bool
    {
        return in_array($this->status, ['draft', 'processing']);
    }

    /**
     * Mark as completed
     */
    public function markAsCompleted(): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
        ]);
    }

    /**
     * Get parties by type
     */
    public function getPartiesByType(string $type)
    {
        return $this->parties()->where('party_type', $type)->get();
    }

    /**
     * Get total estimated value of assets
     */
    public function getTotalEstimatedValue(): float
    {
        return $this->assets()->sum('estimated_value') ?? 0;
    }
}
