@php
$configData = Helper::appClasses();
@endphp
@extends('layouts/layoutMaster')

@section('title', 'Quản lý loại hợp đồng')

<!-- Vendor Style -->
@section('vendor-style')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables.bootstrap5.scss'
])
@endsection

<!-- Page Style -->
@section('page-style')
@vite(['resources/css/contract-types.css'])
@endsection

<!-- Vendor Script -->
@section('vendor-script')
@vite([
  'resources/assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js'
])
@endsection

@section('content')
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header border-bottom">
        <h5 class="card-title mb-0">
          <i class="ri-file-list-line me-2"></i>
          Quản lý loạ<PERSON> hợp đồng
        </h5>
        <div class="d-flex justify-content-between align-items-center row pt-4 gap-4 gap-md-0">
          <div class="col-md-4 col-12">
            @can('contract-types.create')
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#contractTypeModal">
              <i class="ri-add-line me-1"></i>
              Thêm loại hợp đồng
            </button>
            @endcan
          </div>
        </div>
      </div>

      <!-- DataTable -->
      <div class="card-datatable table-responsive">
        <table id="contractTypesTable" class="datatables-contract-types table">
          <thead>
            <tr>
              <th>Tên loại hợp đồng</th>
              <th>Mã</th>
              <th>Mô tả</th>
              <th>Số template</th>
              <th>Trạng thái</th>
              <th>Thứ tự</th>
              <th>Thao tác</th>
            </tr>
          </thead>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Contract Type Modal -->
<div class="modal fade" id="contractTypeModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="contractTypeModalTitle">Thêm loại hợp đồng</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="contractTypeForm">
        <div class="modal-body">
          <input type="hidden" id="contractTypeId" name="id">
          
          <div class="row g-3">
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="text" id="contractTypeName" name="name" class="form-control" required>
                <label for="contractTypeName">Tên loại hợp đồng *</label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="text" id="contractTypeCode" name="code" class="form-control" required>
                <label for="contractTypeCode">Mã loại hợp đồng *</label>
              </div>
            </div>
            <div class="col-12">
              <div class="form-floating form-floating-outline">
                <textarea id="contractTypeDescription" name="description" class="form-control" rows="3"></textarea>
                <label for="contractTypeDescription">Mô tả</label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-floating form-floating-outline">
                <input type="number" id="contractTypeSortOrder" name="sort_order" class="form-control" min="0" value="0">
                <label for="contractTypeSortOrder">Thứ tự sắp xếp</label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-check form-switch mt-3">
                <input type="checkbox" class="form-check-input" id="contractTypeIsActive" name="is_active" checked>
                <label class="form-check-label" for="contractTypeIsActive">Hoạt động</label>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Hủy</button>
          <button type="submit" class="btn btn-primary">
            <i class="ri-save-line me-1"></i>
            Lưu
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Xác nhận xóa</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Bạn có chắc chắn muốn xóa loại hợp đồng này không?</p>
        <p class="text-danger"><small>Hành động này không thể hoàn tác.</small></p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Hủy</button>
        <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Xóa</button>
      </div>
    </div>
  </div>
</div>

<!-- Page Script -->
@section('page-script')
@vite(['resources/js/contract-types.js'])
@endsection

@push('scripts')
<script>
// Pass Laravel routes to JavaScript
window.contractTypesRoutes = {
  index: '{{ route("contract-types.index") }}',
  store: '{{ route("contract-types.store") }}',
  show: function(id) { return `/contract-types/${id}`; },
  update: function(id) { return `/contract-types/${id}`; },
  destroy: function(id) { return `/contract-types/${id}`; }
};
</script>


</script>
@endpush
@endsection
