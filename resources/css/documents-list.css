/* Documents List Styles */

.card-datatable {
  padding: 0;
}

.datatables-documents {
  border-collapse: separate;
  border-spacing: 0;
}

.datatables-documents thead th {
  background-color: var(--bs-gray-50);
  border-bottom: 2px solid var(--bs-gray-200);
  font-weight: 600;
  color: var(--bs-gray-800);
  padding: 1rem 0.75rem;
  vertical-align: middle;
}

.datatables-documents tbody td {
  padding: 0.875rem 0.75rem;
  vertical-align: middle;
  border-bottom: 1px solid var(--bs-gray-100);
}

.datatables-documents tbody tr:hover {
  background-color: var(--bs-gray-50);
}

/* Status badges */
.badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
}

.bg-label-secondary {
  background-color: var(--bs-gray-100) !important;
  color: var(--bs-gray-700) !important;
}

.bg-label-warning {
  background-color: rgba(var(--bs-warning-rgb), 0.1) !important;
  color: var(--bs-warning) !important;
}

.bg-label-success {
  background-color: rgba(var(--bs-success-rgb), 0.1) !important;
  color: var(--bs-success) !important;
}

.bg-label-danger {
  background-color: rgba(var(--bs-danger-rgb), 0.1) !important;
  color: var(--bs-danger) !important;
}

.bg-label-primary {
  background-color: rgba(var(--bs-primary-rgb), 0.1) !important;
  color: var(--bs-primary) !important;
}

/* Action buttons */
.btn-sm {
  padding: 0.375rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 0.25rem;
}

.btn-outline-primary:hover {
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
  color: white;
}

.btn-outline-warning:hover {
  background-color: var(--bs-warning);
  border-color: var(--bs-warning);
  color: white;
}

.btn-outline-success:hover {
  background-color: var(--bs-success);
  border-color: var(--bs-success);
  color: white;
}

.btn-outline-danger:hover {
  background-color: var(--bs-danger);
  border-color: var(--bs-danger);
  color: white;
}

/* Filters section */
.card-body.border-bottom {
  background-color: var(--bs-gray-25);
}

.form-floating-outline {
  position: relative;
}

.form-floating-outline .form-control,
.form-floating-outline .form-select {
  border: 1px solid var(--bs-gray-300);
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-floating-outline .form-control:focus,
.form-floating-outline .form-select:focus {
  border-color: var(--bs-primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
}

.form-floating-outline label {
  background-color: white;
  padding: 0 0.5rem;
  color: var(--bs-gray-600);
  font-weight: 500;
}

/* DataTable customizations */
.dataTables_wrapper .dataTables_length select {
  border: 1px solid var(--bs-gray-300);
  border-radius: 0.375rem;
  padding: 0.375rem 0.75rem;
}

.dataTables_wrapper .dataTables_filter input {
  border: 1px solid var(--bs-gray-300);
  border-radius: 0.375rem;
  padding: 0.375rem 0.75rem;
  margin-left: 0.5rem;
}

.dataTables_wrapper .dataTables_info {
  color: var(--bs-gray-600);
  font-size: 0.875rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
  padding: 0.375rem 0.75rem;
  margin: 0 0.125rem;
  border: 1px solid var(--bs-gray-300);
  border-radius: 0.375rem;
  color: var(--bs-gray-700);
  text-decoration: none;
  transition: all 0.15s ease-in-out;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background-color: var(--bs-gray-100);
  border-color: var(--bs-gray-400);
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
  color: white;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
  color: var(--bs-gray-400);
  cursor: not-allowed;
  opacity: 0.6;
}

/* Loading state */
.dataTables_processing {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 200px;
  margin-left: -100px;
  margin-top: -26px;
  text-align: center;
  padding: 1rem;
  background: white;
  border: 1px solid var(--bs-gray-200);
  border-radius: 0.375rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Empty state */
.dataTables_empty {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--bs-gray-600);
}

.dataTables_empty::before {
  content: "📄";
  display: block;
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .datatables-documents thead th,
  .datatables-documents tbody td {
    padding: 0.5rem 0.375rem;
    font-size: 0.875rem;
  }
  
  .btn-sm {
    padding: 0.25rem 0.375rem;
    font-size: 0.7rem;
  }
  
  .badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
  }
}

/* Select2 customizations */
.select2-container--default .select2-selection--single {
  border: 1px solid var(--bs-gray-300);
  border-radius: 0.375rem;
  height: calc(3.5rem + 2px);
  padding: 0.875rem 0.75rem;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  color: var(--bs-gray-800);
  line-height: 1.75rem;
  padding: 0;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: calc(3.5rem + 2px);
  right: 0.75rem;
}

.select2-container--default.select2-container--focus .select2-selection--single {
  border-color: var(--bs-primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
}

.select2-dropdown {
  border: 1px solid var(--bs-gray-300);
  border-radius: 0.375rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: var(--bs-primary);
}

/* Flatpickr customizations */
.flatpickr-calendar {
  border: 1px solid var(--bs-gray-300);
  border-radius: 0.375rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.flatpickr-day.selected {
  background: var(--bs-primary);
  border-color: var(--bs-primary);
}

.flatpickr-day:hover {
  background: rgba(var(--bs-primary-rgb), 0.1);
}

/* Modal customizations */
.modal-content {
  border: none;
  border-radius: 0.5rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.modal-header {
  border-bottom: 1px solid var(--bs-gray-200);
  padding: 1.5rem;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  border-top: 1px solid var(--bs-gray-200);
  padding: 1rem 1.5rem;
}
