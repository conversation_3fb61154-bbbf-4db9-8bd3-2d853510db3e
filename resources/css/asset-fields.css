/* Asset Fields Management Styles */

/* Modal Styles */
.modal-header {
  border-bottom: 1px solid #e7eaf3;
  padding: 1.5rem;
}

.modal-title {
  font-weight: 600;
  color: #566a7f;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  border-top: 1px solid #e7eaf3;
  padding: 1rem 1.5rem;
}

/* Form Styles */
.form-label {
  font-weight: 500;
  color: #566a7f;
  margin-bottom: 0.5rem;
}

.form-control:focus {
  border-color: #696cff;
  box-shadow: 0 0 0 0.2rem rgba(105, 108, 255, 0.25);
}

.form-select:focus {
  border-color: #696cff;
  box-shadow: 0 0 0 0.2rem rgba(105, 108, 255, 0.25);
}

.form-check-input:checked {
  background-color: #696cff;
  border-color: #696cff;
}

/* Field Type Badges */
.field-type-badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  text-transform: uppercase;
}

.field-type-text { background-color: rgba(105, 108, 255, 0.1); color: #696cff; }
.field-type-number { background-color: rgba(113, 221, 55, 0.1); color: #71dd37; }
.field-type-date { background-color: rgba(255, 171, 0, 0.1); color: #ffab00; }
.field-type-select { background-color: rgba(3, 195, 236, 0.1); color: #03c3ec; }
.field-type-textarea { background-color: rgba(133, 146, 163, 0.1); color: #8592a3; }
.field-type-file { background-color: rgba(255, 62, 29, 0.1); color: #ff3e1d; }
.field-type-checkbox { background-color: rgba(113, 221, 55, 0.1); color: #71dd37; }
.field-type-radio { background-color: rgba(255, 171, 0, 0.1); color: #ffab00; }

/* Options Management */
.options-container {
  border: 1px solid #d9dee3;
  border-radius: 0.375rem;
  padding: 1rem;
  background-color: #f8f9fa;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  background-color: #fff;
  border: 1px solid #e7eaf3;
  border-radius: 0.25rem;
}

.option-item:last-child {
  margin-bottom: 0;
}

.option-input {
  flex: 1;
  border: none;
  outline: none;
  padding: 0.25rem;
}

.option-remove {
  color: #ff3e1d;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
}

.option-remove:hover {
  background-color: rgba(255, 62, 29, 0.1);
}

.add-option-btn {
  width: 100%;
  border: 2px dashed #d9dee3;
  background-color: transparent;
  color: #8592a3;
  padding: 0.75rem;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.add-option-btn:hover {
  border-color: #696cff;
  color: #696cff;
  background-color: rgba(105, 108, 255, 0.05);
}

/* Field Preview */
.field-preview {
  border: 1px solid #e7eaf3;
  border-radius: 0.375rem;
  padding: 1rem;
  background-color: #f8f9fa;
  margin-top: 1rem;
}

.field-preview-title {
  font-weight: 600;
  color: #566a7f;
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
}

.preview-field {
  margin-bottom: 0;
}

.preview-field .form-label {
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.preview-field .form-control,
.preview-field .form-select {
  font-size: 0.875rem;
}

.preview-help-text {
  font-size: 0.75rem;
  color: #8592a3;
  margin-top: 0.25rem;
}

/* Validation Rules */
.validation-rules {
  border: 1px solid #d9dee3;
  border-radius: 0.375rem;
  padding: 1rem;
  background-color: #f8f9fa;
}

.validation-rule {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.validation-rule:last-child {
  margin-bottom: 0;
}

.validation-rule-select {
  flex: 1;
}

.validation-rule-value {
  flex: 1;
}

.validation-rule-remove {
  color: #ff3e1d;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
}

.validation-rule-remove:hover {
  background-color: rgba(255, 62, 29, 0.1);
}

/* Button Styles */
.btn-primary {
  background-color: #696cff;
  border-color: #696cff;
}

.btn-primary:hover {
  background-color: #5f61e6;
  border-color: #5f61e6;
}

.btn-outline-secondary {
  color: #8592a3;
  border-color: #8592a3;
}

.btn-outline-secondary:hover {
  background-color: #8592a3;
  border-color: #8592a3;
  color: #fff;
}

/* Loading States */
.btn-loading {
  position: relative;
  pointer-events: none;
}

.btn-loading::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  margin-left: -8px;
  margin-top: -8px;
  border: 2px solid transparent;
  border-top-color: #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* DataTable Customizations */
.dataTables_wrapper .dataTables_length select {
  padding: 0.4375rem 1.875rem 0.4375rem 0.875rem;
  border: 1px solid #d9dee3;
  border-radius: 0.375rem;
}

.dataTables_wrapper .dataTables_filter input {
  padding: 0.4375rem 0.875rem;
  border: 1px solid #d9dee3;
  border-radius: 0.375rem;
  margin-left: 0.5rem;
}

.table th {
  font-weight: 600;
  color: #566a7f;
  border-bottom: 1px solid #e7eaf3;
  padding: 1rem 0.75rem;
}

.table td {
  padding: 0.75rem;
  border-bottom: 1px solid #e7eaf3;
  vertical-align: middle;
}

/* Badge Styles */
.badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
}

.bg-label-success {
  background-color: rgba(113, 221, 55, 0.1) !important;
  color: #71dd37 !important;
}

.bg-label-secondary {
  background-color: rgba(133, 146, 163, 0.1) !important;
  color: #8592a3 !important;
}

.bg-label-warning {
  background-color: rgba(255, 171, 0, 0.1) !important;
  color: #ffab00 !important;
}

.bg-label-info {
  background-color: rgba(3, 195, 236, 0.1) !important;
  color: #03c3ec !important;
}

/* Action Buttons */
.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 0.25rem;
}

.btn-icon {
  width: 32px;
  height: 32px;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Error States */
.is-invalid {
  border-color: #ff3e1d;
}

.invalid-feedback {
  color: #ff3e1d;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Success States */
.is-valid {
  border-color: #71dd37;
}

.valid-feedback {
  color: #71dd37;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Card Styles */
.card {
  box-shadow: 0 2px 6px 0 rgba(67, 89, 113, 0.12);
  border: 0;
  border-radius: 0.5rem;
}

.card-header {
  background-color: transparent;
  border-bottom: 1px solid #e7eaf3;
  padding: 1.5rem;
}

.card-title {
  font-weight: 600;
  color: #566a7f;
  margin-bottom: 0;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .modal-dialog {
    margin: 1rem;
  }
  
  .table-responsive {
    border: none;
  }
  
  .btn-group-sm > .btn {
    padding: 0.125rem 0.25rem;
    font-size: 0.6875rem;
  }
  
  .option-item {
    flex-direction: column;
    align-items: stretch;
  }
  
  .validation-rule {
    flex-direction: column;
    align-items: stretch;
  }
}

/* Hover Effects */
.table tbody tr:hover {
  background-color: rgba(67, 89, 113, 0.05);
}

.btn:hover {
  transform: translateY(-1px);
  transition: all 0.2s ease-in-out;
}

/* Focus States */
.btn:focus,
.form-control:focus,
.form-select:focus {
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(105, 108, 255, 0.25);
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-up {
  animation: slideUp 0.3s ease-in-out;
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}
