/* Documents Wizard Styles */

.wizard-vertical .bs-stepper-header {
  min-width: 15rem;
}

.wizard-vertical .bs-stepper-content {
  width: 100%;
}

/* Step styling */
.bs-stepper-circle {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: var(--bs-gray-100);
  border: 2px solid var(--bs-gray-300);
  color: var(--bs-gray-600);
  font-weight: 600;
}

.step.active .bs-stepper-circle {
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
  color: white;
}

.step.completed .bs-stepper-circle {
  background-color: var(--bs-success);
  border-color: var(--bs-success);
  color: white;
}

/* Content sections */
.content-header {
  border-bottom: 1px solid var(--bs-gray-200);
  padding-bottom: 1rem;
  margin-bottom: 2rem;
}

.content-header h4 {
  color: var(--bs-gray-800);
  font-weight: 600;
}

.content-header small {
  color: var(--bs-gray-600);
}

/* Template selection */
.template-card {
  border: 2px solid var(--bs-gray-200);
  border-radius: 0.5rem;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.template-card:hover {
  border-color: var(--bs-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.template-card.selected {
  border-color: var(--bs-primary);
  background-color: rgba(var(--bs-primary-rgb), 0.05);
}

.template-card .template-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
}

.template-card .template-title {
  font-weight: 600;
  color: var(--bs-gray-800);
  margin-bottom: 0.5rem;
}

.template-card .template-description {
  color: var(--bs-gray-600);
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.template-card .template-actions {
  display: flex;
  gap: 0.5rem;
}

/* Party and Asset forms */
.party-item,
.asset-item {
  border: 1px solid var(--bs-gray-200);
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1rem;
  position: relative;
  background-color: var(--bs-gray-50);
}

.party-item .remove-btn,
.asset-item .remove-btn {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
}

.party-item h6,
.asset-item h6 {
  color: var(--bs-gray-800);
  font-weight: 600;
  margin-bottom: 1rem;
  padding-right: 2rem;
}

/* Dynamic fields */
.field-group {
  border: 1px solid var(--bs-gray-200);
  border-radius: 0.375rem;
  padding: 1rem;
  margin-bottom: 1rem;
}

.field-group-title {
  font-weight: 600;
  color: var(--bs-gray-800);
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--bs-gray-200);
}

/* QR Scanner */
.qr-scanner-container {
  text-align: center;
  padding: 2rem;
}

#qr-reader {
  border: 2px dashed var(--bs-gray-300);
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1rem;
}

#qr-reader video {
  width: 100%;
  max-width: 400px;
  border-radius: 0.375rem;
}

.qr-result {
  background-color: var(--bs-success-bg-subtle);
  border: 1px solid var(--bs-success-border-subtle);
  color: var(--bs-success-text-emphasis);
  padding: 0.75rem;
  border-radius: 0.375rem;
  margin-top: 1rem;
}

/* Search results */
.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid var(--bs-gray-200);
  border-radius: 0.375rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.search-result-item {
  padding: 0.75rem;
  border-bottom: 1px solid var(--bs-gray-100);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.search-result-item:hover {
  background-color: var(--bs-gray-50);
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-name {
  font-weight: 600;
  color: var(--bs-gray-800);
}

.search-result-details {
  font-size: 0.875rem;
  color: var(--bs-gray-600);
}

/* Review section */
.review-section {
  border: 1px solid var(--bs-gray-200);
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.review-section-title {
  font-weight: 600;
  color: var(--bs-gray-800);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--bs-gray-200);
}

.review-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--bs-gray-100);
}

.review-item:last-child {
  border-bottom: none;
}

.review-label {
  font-weight: 500;
  color: var(--bs-gray-700);
}

.review-value {
  color: var(--bs-gray-800);
  text-align: right;
}

/* Loading states */
.loading-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--bs-gray-300);
  border-radius: 50%;
  border-top-color: var(--bs-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .wizard-vertical .bs-stepper-header {
    min-width: auto;
    width: 100%;
  }
  
  .wizard-vertical {
    flex-direction: column;
  }
  
  .template-card {
    padding: 1rem;
  }
  
  .party-item,
  .asset-item {
    padding: 1rem;
  }
}

/* Form validation styles */
.is-invalid {
  border-color: var(--bs-danger);
}

.invalid-feedback {
  display: block;
  color: var(--bs-danger);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Success states */
.is-valid {
  border-color: var(--bs-success);
}

.valid-feedback {
  display: block;
  color: var(--bs-success);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}
