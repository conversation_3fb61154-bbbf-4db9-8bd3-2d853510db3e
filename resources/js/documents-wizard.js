/**
 * Documents Wizard
 */

'use strict';

(function () {
  // Global variables
  let validationStepper;
  let currentTemplateFields = [];
  let partiesCount = 0;
  let assetsCount = 0;

  // Initialize wizard
  const wizardCreateDocument = document.querySelector('#wizard-create-document');
  if (typeof wizardCreateDocument !== 'undefined' && wizardCreateDocument !== null) {
    // Wizard form
    const wizardCreateDocumentForm = wizardCreateDocument.querySelector('#wizard-create-document-form');
    
    // Wizard steps
    const wizardStep1 = wizardCreateDocumentForm.querySelector('#contract-type-step');
    const wizardStep2 = wizardCreateDocumentForm.querySelector('#template-step');
    const wizardStep3 = wizardCreateDocumentForm.querySelector('#parties-step');
    const wizardStep4 = wizardCreateDocumentForm.querySelector('#assets-step');
    const wizardStep5 = wizardCreateDocumentForm.querySelector('#review-step');
    
    // Wizard navigation buttons
    const wizardNext = [].slice.call(wizardCreateDocumentForm.querySelectorAll('.btn-next'));
    const wizardPrev = [].slice.call(wizardCreateDocumentForm.querySelectorAll('.btn-prev'));

    // Initialize stepper
    validationStepper = new Stepper(wizardCreateDocument, {
      linear: true
    });

    // Step 1: Contract Type validation
    const FormValidation1 = FormValidation.formValidation(wizardStep1, {
      fields: {
        contract_type_id: {
          validators: {
            notEmpty: {
              message: 'Vui lòng chọn loại hợp đồng'
            }
          }
        }
      },
      plugins: {
        trigger: new FormValidation.plugins.Trigger(),
        bootstrap5: new FormValidation.plugins.Bootstrap5({
          eleValidClass: '',
          rowSelector: '.col-12'
        }),
        autoFocus: new FormValidation.plugins.AutoFocus(),
        submitButton: new FormValidation.plugins.SubmitButton()
      }
    }).on('core.form.valid', function () {
      loadTemplatesByContractType();
      validationStepper.next();
    });

    // Step 2: Template validation
    const FormValidation2 = FormValidation.formValidation(wizardStep2, {
      fields: {
        asset_template_id: {
          validators: {
            notEmpty: {
              message: 'Vui lòng chọn template'
            }
          }
        }
      },
      plugins: {
        trigger: new FormValidation.plugins.Trigger(),
        bootstrap5: new FormValidation.plugins.Bootstrap5({
          eleValidClass: '',
          rowSelector: '.col-12'
        }),
        autoFocus: new FormValidation.plugins.AutoFocus(),
        submitButton: new FormValidation.plugins.SubmitButton()
      }
    }).on('core.form.valid', function () {
      loadTemplateFields();
      validationStepper.next();
    });

    // Step 3: Parties validation
    const FormValidation3 = FormValidation.formValidation(wizardStep3, {
      fields: {},
      plugins: {
        trigger: new FormValidation.plugins.Trigger(),
        bootstrap5: new FormValidation.plugins.Bootstrap5({
          eleValidClass: '',
          rowSelector: '.col-md-6'
        }),
        autoFocus: new FormValidation.plugins.AutoFocus(),
        submitButton: new FormValidation.plugins.SubmitButton()
      }
    }).on('core.form.valid', function () {
      if (validateParties()) {
        validationStepper.next();
      }
    });

    // Step 4: Assets validation
    const FormValidation4 = FormValidation.formValidation(wizardStep4, {
      fields: {},
      plugins: {
        trigger: new FormValidation.plugins.Trigger(),
        bootstrap5: new FormValidation.plugins.Bootstrap5({
          eleValidClass: '',
          rowSelector: '.col-md-6'
        }),
        autoFocus: new FormValidation.plugins.AutoFocus(),
        submitButton: new FormValidation.plugins.SubmitButton()
      }
    }).on('core.form.valid', function () {
      if (validateAssets()) {
        generateReviewSummary();
        validationStepper.next();
      }
    });

    // Step 5: Final validation and submission
    const FormValidation5 = FormValidation.formValidation(wizardStep5, {
      fields: {
        title: {
          validators: {
            notEmpty: {
              message: 'Vui lòng nhập tiêu đề hồ sơ'
            }
          }
        },
        confirm_document: {
          validators: {
            notEmpty: {
              message: 'Vui lòng xác nhận thông tin'
            }
          }
        }
      },
      plugins: {
        trigger: new FormValidation.plugins.Trigger(),
        bootstrap5: new FormValidation.plugins.Bootstrap5({
          eleValidClass: '',
          rowSelector: '.col-md-6'
        }),
        autoFocus: new FormValidation.plugins.AutoFocus(),
        submitButton: new FormValidation.plugins.SubmitButton()
      }
    }).on('core.form.valid', function () {
      submitDocument();
    });

    // Navigation event handlers
    wizardNext.forEach(item => {
      item.addEventListener('click', event => {
        switch (validationStepper._currentIndex) {
          case 0:
            FormValidation1.validate();
            break;
          case 1:
            FormValidation2.validate();
            break;
          case 2:
            FormValidation3.validate();
            break;
          case 3:
            FormValidation4.validate();
            break;
          case 4:
            FormValidation5.validate();
            break;
          default:
            break;
        }
      });
    });

    wizardPrev.forEach(item => {
      item.addEventListener('click', event => {
        switch (validationStepper._currentIndex) {
          case 4:
          case 3:
          case 2:
          case 1:
            validationStepper.previous();
            break;
          case 0:
          default:
            break;
        }
      });
    });

    // Initialize Select2 for contract type
    const contractTypeSelect = $('#contractType');
    if (contractTypeSelect.length) {
      contractTypeSelect.select2({
        placeholder: 'Chọn loại hợp đồng',
        allowClear: true
      }).on('change', function () {
        FormValidation1.revalidateField('contract_type_id');
      });
    }

    // Event handlers
    initializeEventHandlers();
  }

  function initializeEventHandlers() {
    // Add party button
    document.getElementById('addPartyBtn')?.addEventListener('click', addPartyForm);
    
    // Add asset button
    document.getElementById('addAssetBtn')?.addEventListener('click', addAssetForm);
    
    // Party search
    const partySearch = document.getElementById('partySearch');
    if (partySearch) {
      partySearch.addEventListener('input', debounce(searchParties, 300));
    }
    
    // Asset search
    const assetSearch = document.getElementById('assetSearch');
    if (assetSearch) {
      assetSearch.addEventListener('input', debounce(searchAssets, 300));
    }

    // QR Scanner buttons
    document.getElementById('qrScanPartyBtn')?.addEventListener('click', () => openQRScanner('party'));
    document.getElementById('qrScanAssetBtn')?.addEventListener('click', () => openQRScanner('asset'));
  }

  function loadTemplatesByContractType() {
    const contractTypeId = document.getElementById('contractType').value;
    if (!contractTypeId) return;

    const container = document.getElementById('templates-container');
    container.innerHTML = '<div class="text-center"><div class="loading-spinner"></div> Đang tải templates...</div>';

    fetch(`/documents/ajax/templates-by-contract-type?contract_type_id=${contractTypeId}`)
      .then(response => response.json())
      .then(templates => {
        container.innerHTML = '';
        
        if (templates.length === 0) {
          container.innerHTML = '<div class="col-12 text-center text-muted">Không có template nào cho loại hợp đồng này</div>';
          return;
        }

        templates.forEach(template => {
          const templateCard = createTemplateCard(template);
          container.appendChild(templateCard);
        });
      })
      .catch(error => {
        console.error('Error loading templates:', error);
        container.innerHTML = '<div class="col-12 text-center text-danger">Có lỗi xảy ra khi tải templates</div>';
      });
  }

  function createTemplateCard(template) {
    const col = document.createElement('div');
    col.className = 'col-md-6 col-lg-4';
    
    col.innerHTML = `
      <div class="template-card" data-template-id="${template.id}">
        ${template.is_default ? '<span class="badge bg-primary template-badge">Mặc định</span>' : ''}
        <div class="template-title">${template.name}</div>
        <div class="template-description">${template.description || ''}</div>
        <div class="template-actions">
          <button type="button" class="btn btn-sm btn-outline-info" onclick="previewTemplate(${template.id})">
            <i class="ri-eye-line me-1"></i>Preview
          </button>
          <button type="button" class="btn btn-sm btn-primary select-template-btn" data-template-id="${template.id}">
            <i class="ri-check-line me-1"></i>Chọn
          </button>
        </div>
        <input type="radio" name="asset_template_id" value="${template.id}" style="display: none;">
      </div>
    `;

    // Add click handler for template selection
    const selectBtn = col.querySelector('.select-template-btn');
    selectBtn.addEventListener('click', () => selectTemplate(template.id));

    return col;
  }

  function selectTemplate(templateId) {
    // Remove previous selections
    document.querySelectorAll('.template-card').forEach(card => {
      card.classList.remove('selected');
    });

    // Select current template
    const selectedCard = document.querySelector(`[data-template-id="${templateId}"]`);
    selectedCard.classList.add('selected');
    
    // Set radio button value
    const radio = selectedCard.querySelector('input[type="radio"]');
    radio.checked = true;

    // Trigger validation
    const event = new Event('change', { bubbles: true });
    radio.dispatchEvent(event);
  }

  function loadTemplateFields() {
    const templateId = document.querySelector('input[name="asset_template_id"]:checked')?.value;
    if (!templateId) return;

    fetch(`/documents/ajax/template-fields?template_id=${templateId}`)
      .then(response => response.json())
      .then(data => {
        currentTemplateFields = data.grouped_fields;
        // Initialize first party and asset forms
        addPartyForm();
        addAssetForm();
      })
      .catch(error => {
        console.error('Error loading template fields:', error);
      });
  }

  function addPartyForm() {
    partiesCount++;
    const container = document.getElementById('parties-container');
    
    const partyDiv = document.createElement('div');
    partyDiv.className = 'party-item';
    partyDiv.dataset.partyIndex = partiesCount;
    
    partyDiv.innerHTML = `
      <button type="button" class="btn btn-sm btn-outline-danger remove-btn" onclick="removeParty(${partiesCount})">
        <i class="ri-close-line"></i>
      </button>
      <h6>Đương sự #${partiesCount}</h6>
      <div class="row g-3">
        <div class="col-md-6">
          <div class="form-floating form-floating-outline">
            <select name="parties[${partiesCount}][party_type]" class="form-select">
              <option value="party_a">Bên A</option>
              <option value="party_b">Bên B</option>
              <option value="witness">Người chứng kiến</option>
              <option value="other">Khác</option>
            </select>
            <label>Loại đương sự</label>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-floating form-floating-outline">
            <input type="text" name="parties[${partiesCount}][full_name]" class="form-control" required>
            <label>Họ tên *</label>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-floating form-floating-outline">
            <input type="number" name="parties[${partiesCount}][birth_year]" class="form-control" min="1900" max="${new Date().getFullYear()}" required>
            <label>Năm sinh *</label>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-floating form-floating-outline">
            <input type="text" name="parties[${partiesCount}][id_number]" class="form-control" required>
            <label>Số CCCD/Hộ chiếu *</label>
          </div>
        </div>
        <div class="col-12">
          <div class="form-floating form-floating-outline">
            <textarea name="parties[${partiesCount}][current_address]" class="form-control" required></textarea>
            <label>Nơi cư trú hiện tại *</label>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-floating form-floating-outline">
            <input type="text" name="parties[${partiesCount}][phone]" class="form-control">
            <label>Số điện thoại</label>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-floating form-floating-outline">
            <input type="email" name="parties[${partiesCount}][email]" class="form-control">
            <label>Email</label>
          </div>
        </div>
      </div>
    `;
    
    container.appendChild(partyDiv);
  }

  function addAssetForm() {
    assetsCount++;
    const container = document.getElementById('assets-container');
    
    const assetDiv = document.createElement('div');
    assetDiv.className = 'asset-item';
    assetDiv.dataset.assetIndex = assetsCount;
    
    let fieldsHtml = '';
    if (currentTemplateFields) {
      Object.keys(currentTemplateFields).forEach(groupName => {
        fieldsHtml += `<div class="field-group">
          <div class="field-group-title">${groupName}</div>
          <div class="row g-3">`;
        
        currentTemplateFields[groupName].forEach(field => {
          fieldsHtml += generateFieldHtml(field, assetsCount);
        });
        
        fieldsHtml += '</div></div>';
      });
    }
    
    assetDiv.innerHTML = `
      <button type="button" class="btn btn-sm btn-outline-danger remove-btn" onclick="removeAsset(${assetsCount})">
        <i class="ri-close-line"></i>
      </button>
      <h6>Tài sản #${assetsCount}</h6>
      <div class="row g-3 mb-3">
        <div class="col-md-8">
          <div class="form-floating form-floating-outline">
            <input type="text" name="assets[${assetsCount}][asset_name]" class="form-control" required>
            <label>Tên tài sản *</label>
          </div>
        </div>
        <div class="col-md-4">
          <div class="form-floating form-floating-outline">
            <input type="text" name="assets[${assetsCount}][asset_code]" class="form-control">
            <label>Mã tài sản</label>
          </div>
        </div>
      </div>
      ${fieldsHtml}
    `;
    
    container.appendChild(assetDiv);
  }

  function generateFieldHtml(field, assetIndex) {
    const fieldName = `assets[${assetIndex}][field_values][${field.name}]`;
    const isRequired = field.pivot.is_required;
    const requiredAttr = isRequired ? 'required' : '';
    const requiredLabel = isRequired ? ' *' : '';
    
    let html = '<div class="col-md-6">';
    
    switch (field.type) {
      case 'text':
        html += `
          <div class="form-floating form-floating-outline">
            <input type="text" name="${fieldName}" class="form-control" ${requiredAttr} placeholder="${field.placeholder || ''}">
            <label>${field.label}${requiredLabel}</label>
          </div>`;
        break;
        
      case 'number':
        html += `
          <div class="form-floating form-floating-outline">
            <input type="number" name="${fieldName}" class="form-control" ${requiredAttr} placeholder="${field.placeholder || ''}">
            <label>${field.label}${requiredLabel}</label>
          </div>`;
        break;
        
      case 'date':
        html += `
          <div class="form-floating form-floating-outline">
            <input type="date" name="${fieldName}" class="form-control" ${requiredAttr}>
            <label>${field.label}${requiredLabel}</label>
          </div>`;
        break;
        
      case 'select':
        html += `
          <div class="form-floating form-floating-outline">
            <select name="${fieldName}" class="form-select" ${requiredAttr}>
              <option value="">Chọn...</option>`;
        
        if (field.options) {
          Object.keys(field.options).forEach(key => {
            html += `<option value="${key}">${field.options[key]}</option>`;
          });
        }
        
        html += `</select>
            <label>${field.label}${requiredLabel}</label>
          </div>`;
        break;
        
      case 'textarea':
        html += `
          <div class="form-floating form-floating-outline">
            <textarea name="${fieldName}" class="form-control" ${requiredAttr} placeholder="${field.placeholder || ''}"></textarea>
            <label>${field.label}${requiredLabel}</label>
          </div>`;
        break;
        
      default:
        html += `
          <div class="form-floating form-floating-outline">
            <input type="text" name="${fieldName}" class="form-control" ${requiredAttr} placeholder="${field.placeholder || ''}">
            <label>${field.label}${requiredLabel}</label>
          </div>`;
    }
    
    if (field.help_text) {
      html += `<small class="text-muted">${field.help_text}</small>`;
    }
    
    html += '</div>';
    return html;
  }

  // Global functions for remove buttons
  window.removeParty = function(index) {
    const partyItem = document.querySelector(`[data-party-index="${index}"]`);
    if (partyItem) {
      partyItem.remove();
    }
  };

  window.removeAsset = function(index) {
    const assetItem = document.querySelector(`[data-asset-index="${index}"]`);
    if (assetItem) {
      assetItem.remove();
    }
  };

  function validateParties() {
    const parties = document.querySelectorAll('.party-item');
    if (parties.length === 0) {
      alert('Vui lòng thêm ít nhất một đương sự');
      return false;
    }
    return true;
  }

  function validateAssets() {
    const assets = document.querySelectorAll('.asset-item');
    if (assets.length === 0) {
      alert('Vui lòng thêm ít nhất một tài sản');
      return false;
    }
    return true;
  }

  function generateReviewSummary() {
    const summaryContainer = document.getElementById('review-summary');
    // Implementation for generating review summary
    summaryContainer.innerHTML = '<p>Tóm tắt thông tin sẽ được hiển thị ở đây...</p>';
  }

  function submitDocument() {
    const formData = new FormData(document.getElementById('wizard-create-document-form'));
    
    // Convert FormData to JSON for easier handling
    const data = {};
    for (let [key, value] of formData.entries()) {
      data[key] = value;
    }

    fetch('/documents/store-from-wizard', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
      },
      body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
      if (result.success) {
        alert('Hồ sơ đã được tạo thành công!');
        window.location.href = result.redirect_url;
      } else {
        alert('Có lỗi xảy ra: ' + result.message);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      alert('Có lỗi xảy ra khi tạo hồ sơ');
    });
  }

  // Utility functions
  function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  function searchParties(event) {
    const search = event.target.value;
    if (search.length < 2) return;
    
    // Implementation for searching parties
    console.log('Searching parties:', search);
  }

  function searchAssets(event) {
    const search = event.target.value;
    if (search.length < 2) return;
    
    // Implementation for searching assets
    console.log('Searching assets:', search);
  }

  function openQRScanner(type) {
    // Implementation for QR scanner
    console.log('Opening QR scanner for:', type);
    $('#qrScannerModal').modal('show');
  }

  // Global function for template preview
  window.previewTemplate = function(templateId) {
    console.log('Previewing template:', templateId);
    $('#templatePreviewModal').modal('show');
  };

})();
