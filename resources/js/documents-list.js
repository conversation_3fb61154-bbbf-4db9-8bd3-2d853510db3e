/**
 * Documents List functionality
 */

'use strict';

(function () {
  let documentsTable;
  let deleteDocumentId = null;

  // Initialize when document ready
  $(document).ready(function() {
    initializeDataTable();
    initializeFilters();
    initializeEventHandlers();
  });

  function initializeDataTable() {
    documentsTable = $('#documentsTable').DataTable({
      processing: true,
      serverSide: true,
      ajax: {
        url: window.location.pathname,
        data: function(d) {
          d.contract_type_id = $('#filterContractType').val();
          d.status = $('#filterStatus').val();
          d.date_from = $('#filterDateFrom').val();
          d.date_to = $('#filterDateTo').val();
          d.search = $('#globalSearch').val();
        },
        error: function(xhr, error, thrown) {
          console.error('DataTable AJAX error:', error);
          showToast('error', 'Có lỗi x<PERSON>y ra khi tải dữ liệu');
        }
      },
      columns: [
        { 
          data: 'document_number', 
          name: 'document_number',
          render: function(data, type, row) {
            return `<span class="fw-medium">${data}</span>`;
          }
        },
        { 
          data: 'title', 
          name: 'title',
          render: function(data, type, row) {
            return `<span class="text-truncate" style="max-width: 200px;" title="${data}">${data}</span>`;
          }
        },
        { 
          data: 'contract_type', 
          name: 'contract_type', 
          orderable: false,
          render: function(data, type, row) {
            return data || '<span class="text-muted">-</span>';
          }
        },
        { 
          data: 'parties_count', 
          name: 'parties_count', 
          orderable: false,
          className: 'text-center',
          render: function(data, type, row) {
            return `<span class="badge bg-label-info">${data}</span>`;
          }
        },
        { 
          data: 'status_badge', 
          name: 'status', 
          orderable: false,
          className: 'text-center'
        },
        { 
          data: 'created_by', 
          name: 'created_by', 
          orderable: false,
          render: function(data, type, row) {
            return data || '<span class="text-muted">-</span>';
          }
        },
        { 
          data: 'created_at', 
          name: 'created_at',
          render: function(data, type, row) {
            if (type === 'display') {
              const date = new Date(data);
              return date.toLocaleDateString('vi-VN') + '<br><small class="text-muted">' + 
                     date.toLocaleTimeString('vi-VN', {hour: '2-digit', minute: '2-digit'}) + '</small>';
            }
            return data;
          }
        },
        { 
          data: 'action', 
          name: 'action', 
          orderable: false, 
          searchable: false,
          className: 'text-center'
        }
      ],
      order: [[6, 'desc']], // Sort by created_at desc
      pageLength: 25,
      lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
      dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6 d-flex justify-content-center justify-content-md-end"f>>t<"row"<"col-sm-12 col-md-6"i><"col-sm-12 col-md-6"p>>',
      language: {
        url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/vi.json',
        processing: '<div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Đang tải...</span></div></div>',
        emptyTable: '<div class="text-center py-4"><i class="ri-file-list-line ri-48px text-muted mb-3"></i><br>Chưa có hồ sơ nào</div>',
        zeroRecords: '<div class="text-center py-4"><i class="ri-search-line ri-48px text-muted mb-3"></i><br>Không tìm thấy hồ sơ phù hợp</div>'
      },
      responsive: true,
      columnDefs: [
        {
          targets: [3, 4, 7], // Parties count, Status, Action columns
          className: 'text-center'
        },
        {
          targets: [6], // Created at column
          className: 'text-nowrap'
        }
      ],
      drawCallback: function(settings) {
        // Initialize tooltips for truncated text
        $('[title]').tooltip();
      }
    });
  }

  function initializeFilters() {
    // Initialize flatpickr for date inputs
    $('.flatpickr-date').flatpickr({
      dateFormat: 'Y-m-d',
      allowInput: true,
      locale: 'vn'
    });

    // Initialize select2
    $('#filterContractType, #filterStatus').select2({
      placeholder: 'Chọn...',
      allowClear: true,
      width: '100%'
    });
  }

  function initializeEventHandlers() {
    // Filter change events
    $('#filterContractType, #filterStatus, #filterDateFrom, #filterDateTo').on('change', function() {
      documentsTable.ajax.reload();
    });

    // Global search with debounce
    $('#globalSearch').on('keyup', debounce(function() {
      documentsTable.ajax.reload();
    }, 500));

    // Clear filters
    $('#clearFilters').on('click', function() {
      $('#filterContractType, #filterStatus').val('').trigger('change');
      $('#filterDateFrom, #filterDateTo, #globalSearch').val('');
      documentsTable.ajax.reload();
      showToast('info', 'Đã xóa tất cả bộ lọc');
    });

    // Export button
    $('#exportBtn').on('click', function() {
      exportDocuments();
    });

    // Delete confirmation
    $('#confirmDeleteBtn').on('click', function() {
      if (deleteDocumentId) {
        performDeleteDocument(deleteDocumentId);
      }
    });

    // Handle modal events
    $('#deleteModal').on('hidden.bs.modal', function() {
      deleteDocumentId = null;
    });
  }

  function exportDocuments() {
    const filters = {
      contract_type_id: $('#filterContractType').val(),
      status: $('#filterStatus').val(),
      date_from: $('#filterDateFrom').val(),
      date_to: $('#filterDateTo').val(),
      search: $('#globalSearch').val()
    };

    // Build query string
    const queryString = Object.keys(filters)
      .filter(key => filters[key])
      .map(key => `${key}=${encodeURIComponent(filters[key])}`)
      .join('&');

    // Create download link
    const exportUrl = `/documents/export?${queryString}`;
    
    // Show loading state
    const exportBtn = $('#exportBtn');
    const originalText = exportBtn.html();
    exportBtn.html('<i class="ri-loader-line ri-spin me-1"></i>Đang xuất...');
    exportBtn.prop('disabled', true);

    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = exportUrl;
    link.download = `danh-sach-ho-so-${new Date().toISOString().split('T')[0]}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Reset button state
    setTimeout(() => {
      exportBtn.html(originalText);
      exportBtn.prop('disabled', false);
      showToast('success', 'Đã xuất file thành công');
    }, 2000);
  }

  function performDeleteDocument(documentId) {
    const deleteBtn = $('#confirmDeleteBtn');
    const originalText = deleteBtn.html();
    
    // Show loading state
    deleteBtn.html('<i class="ri-loader-line ri-spin me-1"></i>Đang xóa...');
    deleteBtn.prop('disabled', true);

    $.ajax({
      url: `/documents/${documentId}`,
      type: 'DELETE',
      headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
      },
      success: function(response) {
        $('#deleteModal').modal('hide');
        
        if (response.success) {
          documentsTable.ajax.reload();
          showToast('success', response.message);
        } else {
          showToast('error', response.message);
        }
      },
      error: function(xhr) {
        let message = 'Có lỗi xảy ra khi xóa hồ sơ';
        
        if (xhr.responseJSON && xhr.responseJSON.message) {
          message = xhr.responseJSON.message;
        } else if (xhr.status === 403) {
          message = 'Bạn không có quyền xóa hồ sơ này';
        } else if (xhr.status === 404) {
          message = 'Hồ sơ không tồn tại';
        }
        
        showToast('error', message);
        $('#deleteModal').modal('hide');
      },
      complete: function() {
        // Reset button state
        deleteBtn.html(originalText);
        deleteBtn.prop('disabled', false);
      }
    });
  }

  // Utility functions
  function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  function showToast(type, message) {
    // Create toast element
    const toastId = 'toast-' + Date.now();
    const toastHtml = `
      <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="d-flex">
          <div class="toast-body">
            <i class="ri-${getToastIcon(type)}-line me-2"></i>
            ${message}
          </div>
          <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
      </div>
    `;

    // Add to toast container or create one
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
      toastContainer = document.createElement('div');
      toastContainer.id = 'toast-container';
      toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
      toastContainer.style.zIndex = '9999';
      document.body.appendChild(toastContainer);
    }

    toastContainer.insertAdjacentHTML('beforeend', toastHtml);

    // Show toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
      autohide: true,
      delay: type === 'error' ? 5000 : 3000
    });
    
    toast.show();

    // Remove from DOM after hidden
    toastElement.addEventListener('hidden.bs.toast', function() {
      toastElement.remove();
    });
  }

  function getToastIcon(type) {
    const icons = {
      success: 'check-circle',
      error: 'error-warning',
      warning: 'alert',
      info: 'information'
    };
    return icons[type] || 'information';
  }

  // Global function for delete button in DataTable
  window.deleteDocument = function(documentId) {
    deleteDocumentId = documentId;
    $('#deleteModal').modal('show');
  };

  // Global function for view document
  window.viewDocument = function(documentId) {
    window.location.href = `/documents/${documentId}`;
  };

  // Global function for edit document
  window.editDocument = function(documentId) {
    window.location.href = `/documents/${documentId}/edit`;
  };

  // Global function for export single document
  window.exportDocument = function(documentId) {
    const link = document.createElement('a');
    link.href = `/documents/${documentId}/export`;
    link.download = `ho-so-${documentId}.docx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showToast('info', 'Đang tạo file xuất...');
  };

})();
