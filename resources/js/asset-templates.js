/**
 * Asset Templates Management JavaScript
 * Handles CRUD operations for asset templates with drag-drop fields and preview
 */

// Global variables
let assetTemplatesTable;
let deleteAssetTemplateId = null;
let isEditMode = false;
let availableFields = [];
let templateFields = [];
let fieldIndex = 0;

// Initialize when DOM is ready
$(document).ready(function() {
  initializeDataTable();
  initializeEventHandlers();
  loadAvailableFields();
});

/**
 * Initialize DataTable for asset templates
 */
function initializeDataTable() {
  assetTemplatesTable = $('#assetTemplatesTable').DataTable({
    processing: true,
    serverSide: true,
    ajax: {
      url: window.assetTemplatesRoutes.index,
      error: function(xhr, error, thrown) {
        console.error('DataTable AJAX error:', error);
        showToast('error', 'Có lỗi xảy ra khi tải dữ liệu');
      }
    },
    columns: [
      { 
        data: 'name', 
        name: 'name',
        render: function(data, type, row) {
          return `<strong>${data}</strong>`;
        }
      },
      { 
        data: 'contract_type_name', 
        name: 'contract_type_name',
        render: function(data, type, row) {
          return data || '<span class="text-muted">-</span>';
        }
      },
      { 
        data: 'fields_count', 
        name: 'fields_count', 
        orderable: false,
        className: 'text-center',
        render: function(data, type, row) {
          return `<span class="badge bg-label-info">${data}</span>`;
        }
      },
      { 
        data: 'documents_count', 
        name: 'documents_count', 
        orderable: false,
        className: 'text-center',
        render: function(data, type, row) {
          return `<span class="badge bg-label-secondary">${data}</span>`;
        }
      },
      { 
        data: 'default_badge', 
        name: 'is_default', 
        orderable: false,
        className: 'text-center'
      },
      { 
        data: 'status_badge', 
        name: 'is_active', 
        orderable: false,
        className: 'text-center'
      },
      { 
        data: 'sort_order', 
        name: 'sort_order',
        className: 'text-center'
      },
      { 
        data: 'action', 
        name: 'action', 
        orderable: false, 
        searchable: false,
        className: 'text-center'
      }
    ],
    order: [[6, 'asc'], [0, 'asc']], // Sort by sort_order, then name
    language: {
      url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/vi.json'
    },
    responsive: true,
    pageLength: 25,
    lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
    dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
    drawCallback: function(settings) {
      // Add fade-in animation to new rows
      $('#assetTemplatesTable tbody tr').addClass('fade-in');
    }
  });
}

/**
 * Initialize event handlers
 */
function initializeEventHandlers() {
  // Form submission
  $('#assetTemplateForm').on('submit', function(e) {
    e.preventDefault();
    saveAssetTemplate();
  });

  // Modal reset when hidden
  $('#assetTemplateModal').on('hidden.bs.modal', function() {
    resetForm();
  });

  // Delete confirmation
  $('#confirmDeleteBtn').on('click', function() {
    if (deleteAssetTemplateId) {
      performDeleteAssetTemplate(deleteAssetTemplateId);
    }
  });

  // Preview button
  $('#previewTemplateBtn').on('click', function() {
    generateTemplatePreview();
  });

  // Form input changes
  $('#assetTemplateName, #assetTemplateDescription').on('input', function() {
    validateField(this);
  });

  // Initialize drag and drop
  initializeDragAndDrop();
}

/**
 * Load available fields
 */
function loadAvailableFields() {
  // Create a simple endpoint to get all fields without DataTables format
  $.ajax({
    url: '/asset-fields',
    type: 'GET',
    data: {
      format: 'simple' // Add this parameter to get simple array format
    },
    success: function(response) {
      // Handle DataTables response format
      if (response.data && Array.isArray(response.data)) {
        availableFields = response.data;
      } else if (Array.isArray(response)) {
        availableFields = response;
      } else {
        availableFields = [];
      }
      renderAvailableFields();
    },
    error: function(xhr) {
      console.error('Error loading fields:', xhr);
      showToast('error', 'Không thể tải danh sách fields');
      // Fallback: try to get fields from a different endpoint or use empty array
      availableFields = [];
      renderAvailableFields();
    }
  });
}

/**
 * Render available fields
 */
function renderAvailableFields() {
  const container = document.getElementById('availableFieldsList');
  container.innerHTML = '';
  
  if (availableFields.length === 0) {
    container.innerHTML = '<p class="text-muted text-center">Không có fields nào</p>';
    return;
  }
  
  availableFields.forEach(field => {
    const fieldElement = createFieldElement(field, 'available');
    container.appendChild(fieldElement);
  });
}

/**
 * Create field element
 */
function createFieldElement(field, type = 'available') {
  const div = document.createElement('div');
  div.className = 'field-item';
  div.draggable = true;
  div.dataset.fieldId = field.id;
  div.dataset.fieldType = type;
  
  const typeClass = `field-type-${field.type}`;
  
  div.innerHTML = `
    <div class="field-item-header">
      <h6 class="field-item-title">${field.label}</h6>
      <span class="field-item-type ${typeClass}">${field.type}</span>
    </div>
    <p class="field-item-description">${field.name}</p>
    ${type === 'template' ? `
      <div class="template-field-config">
        <div class="config-row">
          <input type="text" class="config-input" placeholder="Nhóm" value="${field.group_name || 'Thông tin cơ bản'}" data-config="group_name">
          <input type="number" class="config-input" placeholder="Thứ tự" value="${field.sort_order || 0}" data-config="sort_order" min="0" style="width: 80px;">
        </div>
        <div class="config-row">
          <label class="config-label">
            <input type="checkbox" class="config-checkbox" data-config="is_required" ${field.is_required ? 'checked' : ''}>
            Bắt buộc
          </label>
        </div>
      </div>
      <div class="field-item-actions">
        <button type="button" class="field-action-btn remove-btn" onclick="removeFieldFromTemplate(${field.id})">
          <i class="ri-delete-bin-line"></i>
        </button>
      </div>
    ` : ''}
  `;
  
  return div;
}

/**
 * Initialize drag and drop
 */
function initializeDragAndDrop() {
  const availableContainer = document.getElementById('availableFieldsList');
  const templateContainer = document.getElementById('templateFieldsList');
  
  // Drag start
  $(document).on('dragstart', '.field-item', function(e) {
    e.originalEvent.dataTransfer.setData('text/plain', '');
    $(this).addClass('dragging');
    e.originalEvent.dataTransfer.effectAllowed = 'move';
  });
  
  // Drag end
  $(document).on('dragend', '.field-item', function(e) {
    $(this).removeClass('dragging');
  });
  
  // Drag over
  $(document).on('dragover', '#templateFieldsList, #templateDropZone', function(e) {
    e.preventDefault();
    e.originalEvent.dataTransfer.dropEffect = 'move';
    $(this).addClass('drag-over');
  });
  
  // Drag leave
  $(document).on('dragleave', '#templateFieldsList, #templateDropZone', function(e) {
    $(this).removeClass('drag-over');
  });
  
  // Drop
  $(document).on('drop', '#templateFieldsList, #templateDropZone', function(e) {
    e.preventDefault();
    $(this).removeClass('drag-over');
    
    const draggingElement = document.querySelector('.field-item.dragging');
    if (draggingElement && draggingElement.dataset.fieldType === 'available') {
      const fieldId = draggingElement.dataset.fieldId;
      const field = availableFields.find(f => f.id == fieldId);
      if (field) {
        addFieldToTemplate(field);
      }
    }
  });
}

/**
 * Add field to template
 */
function addFieldToTemplate(field) {
  // Check if field already exists in template
  if (templateFields.find(f => f.id == field.id)) {
    showToast('warning', 'Field này đã có trong template');
    return;
  }
  
  // Add to template fields array
  const templateField = {
    ...field,
    group_name: 'Thông tin cơ bản',
    sort_order: templateFields.length,
    is_required: false
  };
  
  templateFields.push(templateField);
  
  // Re-render template fields
  renderTemplateFields();
  
  showToast('success', `Đã thêm field "${field.label}" vào template`);
}

/**
 * Remove field from template
 */
function removeFieldFromTemplate(fieldId) {
  templateFields = templateFields.filter(f => f.id != fieldId);
  renderTemplateFields();
  
  const field = availableFields.find(f => f.id == fieldId);
  if (field) {
    showToast('success', `Đã xóa field "${field.label}" khỏi template`);
  }
}

/**
 * Render template fields
 */
function renderTemplateFields() {
  const container = document.getElementById('templateFieldsList');
  const dropZone = document.getElementById('templateDropZone');
  
  // Clear container but keep drop zone
  Array.from(container.children).forEach(child => {
    if (child.id !== 'templateDropZone') {
      child.remove();
    }
  });
  
  if (templateFields.length === 0) {
    dropZone.style.display = 'flex';
    return;
  }
  
  dropZone.style.display = 'none';
  
  // Sort fields by sort_order
  const sortedFields = [...templateFields].sort((a, b) => a.sort_order - b.sort_order);
  
  sortedFields.forEach(field => {
    const fieldElement = createFieldElement(field, 'template');
    container.appendChild(fieldElement);
  });
  
  // Add event listeners for config changes
  $(container).find('.config-input, .config-checkbox').on('change', function() {
    updateFieldConfig(this);
  });
}

/**
 * Update field configuration
 */
function updateFieldConfig(element) {
  const fieldItem = element.closest('.field-item');
  const fieldId = fieldItem.dataset.fieldId;
  const configType = element.dataset.config;
  const value = element.type === 'checkbox' ? element.checked : element.value;
  
  const field = templateFields.find(f => f.id == fieldId);
  if (field) {
    field[configType] = value;
  }
}

/**
 * Save asset template (create or update)
 */
function saveAssetTemplate() {
  const form = document.getElementById('assetTemplateForm');
  const submitBtn = form.querySelector('button[type="submit"]');

  // Show loading state
  setButtonLoading(submitBtn, true);

  // Clear previous validation errors
  clearValidationErrors();

  const formData = new FormData(form);
  const data = {};

  // Convert FormData to object
  for (let [key, value] of formData.entries()) {
    data[key] = value;
  }

  // Convert checkboxes to boolean
  data.is_active = document.getElementById('assetTemplateIsActive').checked;
  data.is_default = document.getElementById('assetTemplateIsDefault').checked;

  // Add template fields
  data.fields = templateFields.map((field, index) => ({
    field_id: field.id,
    is_required: field.is_required || false,
    sort_order: field.sort_order || index,
    group_name: field.group_name || 'Thông tin cơ bản'
  }));

  const url = isEditMode ?
    window.assetTemplatesRoutes.update(data.id) :
    window.assetTemplatesRoutes.store;

  const method = isEditMode ? 'PUT' : 'POST';

  $.ajax({
    url: url,
    type: method,
    data: data,
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    },
    success: function(response) {
      setButtonLoading(submitBtn, false);

      if (response.success) {
        $('#assetTemplateModal').modal('hide');
        assetTemplatesTable.ajax.reload(null, false);
        showToast('success', response.message);
      } else {
        showToast('error', response.message);
      }
    },
    error: function(xhr) {
      setButtonLoading(submitBtn, false);

      if (xhr.status === 422) {
        // Validation errors
        const errors = xhr.responseJSON.errors;
        displayValidationErrors(errors);
      } else {
        let message = 'Có lỗi xảy ra';

        if (xhr.responseJSON && xhr.responseJSON.message) {
          message = xhr.responseJSON.message;
        }

        showToast('error', message);
      }
    }
  });
}

/**
 * View template
 */
function viewTemplate(id) {
  $.ajax({
    url: window.assetTemplatesRoutes.show(id),
    type: 'GET',
    success: function(response) {
      const template = response.template;
      const groupedFields = response.grouped_fields;

      // Show template details in a modal or redirect to view page
      showTemplateDetails(template, groupedFields);
    },
    error: function(xhr) {
      showToast('error', 'Không thể tải thông tin template');
    }
  });
}

/**
 * Edit template
 */
function editTemplate(id) {
  isEditMode = true;
  $('#assetTemplateModalTitle').text('Sửa template');

  // Show loading in modal
  showModalLoading(true);

  $.ajax({
    url: window.assetTemplatesRoutes.show(id),
    type: 'GET',
    success: function(response) {
      showModalLoading(false);

      const template = response.template;

      $('#assetTemplateId').val(template.id);
      $('#assetTemplateContractType').val(template.contract_type_id);
      $('#assetTemplateName').val(template.name);
      $('#assetTemplateDescription').val(template.description);
      $('#assetTemplateSortOrder').val(template.sort_order);
      $('#assetTemplateIsDefault').prop('checked', template.is_default);
      $('#assetTemplateIsActive').prop('checked', template.is_active);

      // Load template fields
      templateFields = template.asset_fields.map(field => ({
        ...field,
        group_name: field.pivot.group_name,
        sort_order: field.pivot.sort_order,
        is_required: field.pivot.is_required
      }));

      renderTemplateFields();

      $('#assetTemplateModal').modal('show');
    },
    error: function(xhr) {
      showModalLoading(false);
      showToast('error', 'Không thể tải thông tin template');
    }
  });
}

/**
 * Delete template
 */
function deleteTemplate(id) {
  deleteAssetTemplateId = id;
  $('#deleteModal').modal('show');
}

/**
 * Perform delete template
 */
function performDeleteAssetTemplate(id) {
  const deleteBtn = document.getElementById('confirmDeleteBtn');
  setButtonLoading(deleteBtn, true);

  $.ajax({
    url: window.assetTemplatesRoutes.destroy(id),
    type: 'DELETE',
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    },
    success: function(response) {
      setButtonLoading(deleteBtn, false);
      $('#deleteModal').modal('hide');

      if (response.success) {
        assetTemplatesTable.ajax.reload(null, false);
        showToast('success', response.message);
      } else {
        showToast('error', response.message);
      }
    },
    error: function(xhr) {
      setButtonLoading(deleteBtn, false);

      let message = 'Có lỗi xảy ra khi xóa template';

      if (xhr.responseJSON && xhr.responseJSON.message) {
        message = xhr.responseJSON.message;
      }

      showToast('error', message);
      $('#deleteModal').modal('hide');
    }
  });
}

/**
 * Generate template preview
 */
function generateTemplatePreview() {
  if (templateFields.length === 0) {
    showToast('warning', 'Vui lòng thêm ít nhất một field vào template');
    return;
  }

  const previewContainer = document.getElementById('templatePreview');
  const previewContent = document.getElementById('templatePreviewContent');

  // Group fields by group_name
  const groupedFields = {};
  templateFields.forEach(field => {
    const groupName = field.group_name || 'Thông tin cơ bản';
    if (!groupedFields[groupName]) {
      groupedFields[groupName] = [];
    }
    groupedFields[groupName].push(field);
  });

  // Sort fields within each group by sort_order
  Object.keys(groupedFields).forEach(groupName => {
    groupedFields[groupName].sort((a, b) => a.sort_order - b.sort_order);
  });

  // Generate preview HTML
  let previewHtml = '';
  Object.keys(groupedFields).forEach(groupName => {
    previewHtml += `
      <div class="preview-group">
        <h6 class="preview-group-title">${groupName}</h6>
        <div class="row g-3">
    `;

    groupedFields[groupName].forEach(field => {
      const requiredMark = field.is_required ? '<span class="required-indicator">*</span>' : '';
      previewHtml += `<div class="col-md-6">`;
      previewHtml += generateFieldPreviewHtml(field, requiredMark);
      previewHtml += `</div>`;
    });

    previewHtml += `
        </div>
      </div>
    `;
  });

  previewContent.innerHTML = previewHtml;
  previewContainer.style.display = 'block';

  // Scroll to preview
  previewContainer.scrollIntoView({ behavior: 'smooth' });
}

/**
 * Generate field preview HTML
 */
function generateFieldPreviewHtml(field, requiredMark) {
  const label = `${field.label}${requiredMark}`;
  const placeholder = field.placeholder || '';
  const helpText = field.help_text ? `<div class="preview-help-text">${field.help_text}</div>` : '';

  switch (field.type) {
    case 'text':
      return `
        <div class="preview-field">
          <label class="form-label">${label}</label>
          <input type="text" class="form-control" placeholder="${placeholder}" ${field.is_required ? 'required' : ''}>
          ${helpText}
        </div>
      `;

    case 'number':
      return `
        <div class="preview-field">
          <label class="form-label">${label}</label>
          <input type="number" class="form-control" placeholder="${placeholder}" ${field.is_required ? 'required' : ''}>
          ${helpText}
        </div>
      `;

    case 'date':
      return `
        <div class="preview-field">
          <label class="form-label">${label}</label>
          <input type="date" class="form-control" ${field.is_required ? 'required' : ''}>
          ${helpText}
        </div>
      `;

    case 'textarea':
      return `
        <div class="preview-field">
          <label class="form-label">${label}</label>
          <textarea class="form-control" rows="3" placeholder="${placeholder}" ${field.is_required ? 'required' : ''}></textarea>
          ${helpText}
        </div>
      `;

    case 'select':
      const selectOptions = field.options ? Object.keys(field.options).map(key =>
        `<option value="${key}">${field.options[key]}</option>`
      ).join('') : '';
      return `
        <div class="preview-field">
          <label class="form-label">${label}</label>
          <select class="form-select" ${field.is_required ? 'required' : ''}>
            <option value="">Chọn...</option>
            ${selectOptions}
          </select>
          ${helpText}
        </div>
      `;

    case 'radio':
      const radioOptions = field.options ? Object.keys(field.options).map((key, index) =>
        `<div class="form-check">
          <input class="form-check-input" type="radio" name="preview_${field.id}" value="${key}" id="preview_${field.id}_${index}">
          <label class="form-check-label" for="preview_${field.id}_${index}">${field.options[key]}</label>
        </div>`
      ).join('') : '';
      return `
        <div class="preview-field">
          <label class="form-label">${label}</label>
          <div>${radioOptions}</div>
          ${helpText}
        </div>
      `;

    case 'checkbox':
      const checkboxOptions = field.options ? Object.keys(field.options).map((key, index) =>
        `<div class="form-check">
          <input class="form-check-input" type="checkbox" value="${key}" id="preview_${field.id}_${index}">
          <label class="form-check-label" for="preview_${field.id}_${index}">${field.options[key]}</label>
        </div>`
      ).join('') : '';
      return `
        <div class="preview-field">
          <label class="form-label">${label}</label>
          <div>${checkboxOptions}</div>
          ${helpText}
        </div>
      `;

    case 'file':
      return `
        <div class="preview-field">
          <label class="form-label">${label}</label>
          <input type="file" class="form-control" ${field.is_required ? 'required' : ''}>
          ${helpText}
        </div>
      `;

    default:
      return `
        <div class="preview-field">
          <label class="form-label">${label}</label>
          <input type="text" class="form-control" placeholder="${placeholder}" ${field.is_required ? 'required' : ''}>
          ${helpText}
        </div>
      `;
  }
}

/**
 * Reset form to initial state
 */
function resetForm() {
  isEditMode = false;
  fieldIndex = 0;
  templateFields = [];

  $('#assetTemplateModalTitle').text('Thêm template mới');
  $('#assetTemplateForm')[0].reset();
  $('#assetTemplateId').val('');
  $('#assetTemplateIsActive').prop('checked', true);

  // Reset template fields
  renderTemplateFields();

  // Hide preview
  document.getElementById('templatePreview').style.display = 'none';

  clearValidationErrors();
}

/**
 * Show template details
 */
function showTemplateDetails(template, groupedFields) {
  // Create a simple modal to show template details
  const modalHtml = `
    <div class="modal fade" id="templateDetailsModal" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Chi tiết Template: ${template.name}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="row g-3 mb-4">
              <div class="col-md-6">
                <strong>Loại hợp đồng:</strong> ${template.contract_type ? template.contract_type.name : '-'}
              </div>
              <div class="col-md-6">
                <strong>Trạng thái:</strong>
                <span class="badge ${template.is_active ? 'bg-label-success' : 'bg-label-secondary'}">
                  ${template.is_active ? 'Hoạt động' : 'Không hoạt động'}
                </span>
              </div>
              <div class="col-12">
                <strong>Mô tả:</strong> ${template.description || '-'}
              </div>
            </div>
            <h6>Fields trong template:</h6>
            <div id="templateFieldsDetails"></div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
          </div>
        </div>
      </div>
    </div>
  `;

  // Remove existing modal if any
  $('#templateDetailsModal').remove();

  // Add modal to body
  $('body').append(modalHtml);

  // Generate fields details
  let fieldsHtml = '';
  Object.keys(groupedFields).forEach(groupName => {
    fieldsHtml += `
      <div class="mb-3">
        <h6 class="text-primary">${groupName}</h6>
        <div class="row g-2">
    `;

    groupedFields[groupName].forEach(field => {
      fieldsHtml += `
        <div class="col-md-6">
          <div class="border rounded p-2">
            <strong>${field.label}</strong>
            <span class="badge field-type-${field.type} ms-2">${field.type}</span>
            ${field.pivot.is_required ? '<span class="badge bg-label-warning ms-1">Bắt buộc</span>' : ''}
            <br>
            <small class="text-muted">${field.name}</small>
          </div>
        </div>
      `;
    });

    fieldsHtml += `
        </div>
      </div>
    `;
  });

  $('#templateFieldsDetails').html(fieldsHtml);

  // Show modal
  $('#templateDetailsModal').modal('show');
}

/**
 * Validate individual field
 */
function validateField(field) {
  const $field = $(field);
  const value = $field.val().trim();

  $field.removeClass('is-invalid is-valid');

  if (field.required && !value) {
    $field.addClass('is-invalid');
    return false;
  }

  if (value) {
    $field.addClass('is-valid');
  }

  return true;
}

/**
 * Display validation errors
 */
function displayValidationErrors(errors) {
  Object.keys(errors).forEach(field => {
    const fieldName = field.charAt(0).toUpperCase() + field.slice(1);
    const $field = $(`#assetTemplate${fieldName}`);
    const $feedback = $field.siblings('.invalid-feedback');

    $field.addClass('is-invalid');

    if ($feedback.length) {
      $feedback.text(errors[field][0]);
    } else {
      $field.after(`<div class="invalid-feedback">${errors[field][0]}</div>`);
    }
  });
}

/**
 * Clear validation errors
 */
function clearValidationErrors() {
  $('#assetTemplateForm .form-control, #assetTemplateForm .form-select').removeClass('is-invalid is-valid');
  $('#assetTemplateForm .invalid-feedback').remove();
}

/**
 * Set button loading state
 */
function setButtonLoading(button, loading) {
  if (loading) {
    button.classList.add('btn-loading');
    button.disabled = true;
  } else {
    button.classList.remove('btn-loading');
    button.disabled = false;
  }
}

/**
 * Show/hide modal loading state
 */
function showModalLoading(show) {
  const modal = document.getElementById('assetTemplateModal');
  const loadingHtml = `
    <div class="d-flex justify-content-center p-4">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Đang tải...</span>
      </div>
    </div>
  `;

  if (show) {
    modal.querySelector('.modal-body').innerHTML = loadingHtml;
  }
}

/**
 * Show toast notification
 */
function showToast(type, message) {
  const toastId = 'toast-' + Date.now();
  const iconClass = type === 'success' ? 'ri-check-line' :
                   type === 'warning' ? 'ri-alert-line' : 'ri-error-warning-line';
  const bgClass = type === 'error' ? 'danger' : type;

  const toastHtml = `
    <div id="${toastId}" class="toast align-items-center text-white bg-${bgClass} border-0" role="alert">
      <div class="d-flex">
        <div class="toast-body">
          <i class="${iconClass} me-2"></i>
          ${message}
        </div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    </div>
  `;

  let toastContainer = document.getElementById('toast-container');
  if (!toastContainer) {
    toastContainer = document.createElement('div');
    toastContainer.id = 'toast-container';
    toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
    toastContainer.style.zIndex = '9999';
    document.body.appendChild(toastContainer);
  }

  toastContainer.insertAdjacentHTML('beforeend', toastHtml);

  const toastElement = document.getElementById(toastId);
  const toast = new bootstrap.Toast(toastElement, {
    autohide: true,
    delay: type === 'error' ? 5000 : 3000
  });

  toast.show();

  toastElement.addEventListener('hidden.bs.toast', function() {
    toastElement.remove();
  });
}

// Export functions to global scope for DataTable buttons
window.viewTemplate = viewTemplate;
window.editTemplate = editTemplate;
window.deleteTemplate = deleteTemplate;
window.removeFieldFromTemplate = removeFieldFromTemplate;
