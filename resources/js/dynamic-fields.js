/**
 * Dynamic Fields functionality
 */

'use strict';

(function () {
  
  // Field type configurations
  const fieldTypeConfigs = {
    text: {
      inputType: 'text',
      hasOptions: false,
      defaultValidation: ['string', 'max:255']
    },
    number: {
      inputType: 'number',
      hasOptions: false,
      defaultValidation: ['numeric']
    },
    date: {
      inputType: 'date',
      hasOptions: false,
      defaultValidation: ['date']
    },
    select: {
      inputType: 'select',
      hasOptions: true,
      defaultValidation: ['string']
    },
    textarea: {
      inputType: 'textarea',
      hasOptions: false,
      defaultValidation: ['string', 'max:1000']
    },
    file: {
      inputType: 'file',
      hasOptions: false,
      defaultValidation: ['file']
    },
    checkbox: {
      inputType: 'checkbox',
      hasOptions: true,
      defaultValidation: ['array']
    },
    radio: {
      inputType: 'radio',
      hasOptions: true,
      defaultValidation: ['string']
    }
  };

  // Initialize dynamic field functionality
  function initializeDynamicFields() {
    // Add event listeners for field type changes
    document.addEventListener('change', function(e) {
      if (e.target.matches('[name="type"]')) {
        handleFieldTypeChange(e.target);
      }
    });

    // Add event listeners for adding/removing options
    document.addEventListener('click', function(e) {
      if (e.target.matches('.add-option-btn')) {
        addFieldOption(e.target);
      } else if (e.target.matches('.remove-option-btn')) {
        removeFieldOption(e.target);
      }
    });

    // Initialize existing field forms
    document.querySelectorAll('[name="type"]').forEach(handleFieldTypeChange);
  }

  function handleFieldTypeChange(selectElement) {
    const fieldType = selectElement.value;
    const formContainer = selectElement.closest('.field-form-container');
    
    if (!formContainer) return;

    const config = fieldTypeConfigs[fieldType];
    if (!config) return;

    // Show/hide options section
    const optionsSection = formContainer.querySelector('.field-options-section');
    if (optionsSection) {
      optionsSection.style.display = config.hasOptions ? 'block' : 'none';
    }

    // Update validation rules suggestions
    updateValidationSuggestions(formContainer, config.defaultValidation);

    // Update placeholder suggestions
    updatePlaceholderSuggestions(formContainer, fieldType);
  }

  function updateValidationSuggestions(container, defaultRules) {
    const validationInput = container.querySelector('[name="validation_rules"]');
    if (validationInput && !validationInput.value) {
      validationInput.placeholder = defaultRules.join('|');
    }
  }

  function updatePlaceholderSuggestions(container, fieldType) {
    const placeholderInput = container.querySelector('[name="placeholder"]');
    if (!placeholderInput) return;

    const suggestions = {
      text: 'Nhập văn bản...',
      number: 'Nhập số...',
      date: 'Chọn ngày...',
      select: 'Chọn một tùy chọn...',
      textarea: 'Nhập mô tả...',
      file: 'Chọn file...',
      checkbox: 'Chọn các tùy chọn...',
      radio: 'Chọn một tùy chọn...'
    };

    if (!placeholderInput.value && suggestions[fieldType]) {
      placeholderInput.placeholder = suggestions[fieldType];
    }
  }

  function addFieldOption(button) {
    const optionsContainer = button.closest('.field-options-section').querySelector('.options-container');
    const optionIndex = optionsContainer.children.length;
    
    const optionDiv = document.createElement('div');
    optionDiv.className = 'option-item row g-2 mb-2';
    optionDiv.innerHTML = `
      <div class="col-md-4">
        <input type="text" name="options[${optionIndex}][key]" class="form-control" placeholder="Giá trị" required>
      </div>
      <div class="col-md-6">
        <input type="text" name="options[${optionIndex}][value]" class="form-control" placeholder="Nhãn hiển thị" required>
      </div>
      <div class="col-md-2">
        <button type="button" class="btn btn-outline-danger btn-sm remove-option-btn w-100">
          <i class="ri-delete-bin-line"></i>
        </button>
      </div>
    `;
    
    optionsContainer.appendChild(optionDiv);
  }

  function removeFieldOption(button) {
    const optionItem = button.closest('.option-item');
    if (optionItem) {
      optionItem.remove();
      
      // Reindex remaining options
      const container = button.closest('.options-container');
      reindexOptions(container);
    }
  }

  function reindexOptions(container) {
    const optionItems = container.querySelectorAll('.option-item');
    optionItems.forEach((item, index) => {
      const keyInput = item.querySelector('[name*="[key]"]');
      const valueInput = item.querySelector('[name*="[value]"]');
      
      if (keyInput) keyInput.name = `options[${index}][key]`;
      if (valueInput) valueInput.name = `options[${index}][value]`;
    });
  }

  // Generate field HTML for forms
  function generateFieldHTML(field, namePrefix = '', value = null) {
    const fieldName = namePrefix ? `${namePrefix}[${field.name}]` : field.name;
    const isRequired = field.is_required || (field.pivot && field.pivot.is_required);
    const requiredAttr = isRequired ? 'required' : '';
    const requiredLabel = isRequired ? ' *' : '';
    
    let html = '';
    
    switch (field.type) {
      case 'text':
        html = `
          <div class="form-floating form-floating-outline">
            <input type="text" 
                   name="${fieldName}" 
                   class="form-control" 
                   ${requiredAttr}
                   placeholder="${field.placeholder || ''}"
                   value="${value || ''}">
            <label>${field.label}${requiredLabel}</label>
          </div>`;
        break;
        
      case 'number':
        html = `
          <div class="form-floating form-floating-outline">
            <input type="number" 
                   name="${fieldName}" 
                   class="form-control" 
                   ${requiredAttr}
                   placeholder="${field.placeholder || ''}"
                   value="${value || ''}">
            <label>${field.label}${requiredLabel}</label>
          </div>`;
        break;
        
      case 'date':
        html = `
          <div class="form-floating form-floating-outline">
            <input type="date" 
                   name="${fieldName}" 
                   class="form-control" 
                   ${requiredAttr}
                   value="${value || ''}">
            <label>${field.label}${requiredLabel}</label>
          </div>`;
        break;
        
      case 'select':
        html = `
          <div class="form-floating form-floating-outline">
            <select name="${fieldName}" class="form-select" ${requiredAttr}>
              <option value="">Chọn...</option>`;
        
        const options = field.custom_options || field.options || {};
        Object.keys(options).forEach(key => {
          const selected = value === key ? 'selected' : '';
          html += `<option value="${key}" ${selected}>${options[key]}</option>`;
        });
        
        html += `</select>
            <label>${field.label}${requiredLabel}</label>
          </div>`;
        break;
        
      case 'textarea':
        html = `
          <div class="form-floating form-floating-outline">
            <textarea name="${fieldName}" 
                      class="form-control" 
                      ${requiredAttr}
                      placeholder="${field.placeholder || ''}"
                      rows="3">${value || ''}</textarea>
            <label>${field.label}${requiredLabel}</label>
          </div>`;
        break;
        
      case 'file':
        html = `
          <div class="form-floating form-floating-outline">
            <input type="file" 
                   name="${fieldName}" 
                   class="form-control" 
                   ${requiredAttr}>
            <label>${field.label}${requiredLabel}</label>
          </div>`;
        break;
        
      case 'checkbox':
        html = '<div class="form-check-group">';
        html += `<label class="form-label">${field.label}${requiredLabel}</label>`;
        
        const checkboxOptions = field.custom_options || field.options || {};
        const checkboxValues = Array.isArray(value) ? value : (value ? [value] : []);
        
        Object.keys(checkboxOptions).forEach(key => {
          const checked = checkboxValues.includes(key) ? 'checked' : '';
          html += `
            <div class="form-check">
              <input type="checkbox" 
                     name="${fieldName}[]" 
                     value="${key}" 
                     class="form-check-input" 
                     ${checked}
                     ${requiredAttr}>
              <label class="form-check-label">${checkboxOptions[key]}</label>
            </div>`;
        });
        
        html += '</div>';
        break;
        
      case 'radio':
        html = '<div class="form-check-group">';
        html += `<label class="form-label">${field.label}${requiredLabel}</label>`;
        
        const radioOptions = field.custom_options || field.options || {};
        Object.keys(radioOptions).forEach(key => {
          const checked = value === key ? 'checked' : '';
          html += `
            <div class="form-check">
              <input type="radio" 
                     name="${fieldName}" 
                     value="${key}" 
                     class="form-check-input" 
                     ${checked}
                     ${requiredAttr}>
              <label class="form-check-label">${radioOptions[key]}</label>
            </div>`;
        });
        
        html += '</div>';
        break;
        
      default:
        html = `
          <div class="form-floating form-floating-outline">
            <input type="text" 
                   name="${fieldName}" 
                   class="form-control" 
                   ${requiredAttr}
                   placeholder="${field.placeholder || ''}"
                   value="${value || ''}">
            <label>${field.label}${requiredLabel}</label>
          </div>`;
    }
    
    // Add help text if available
    if (field.help_text) {
      html += `<small class="form-text text-muted">${field.help_text}</small>`;
    }
    
    return html;
  }

  // Validate field values
  function validateFieldValue(field, value) {
    const errors = [];
    
    // Check required
    if ((field.is_required || (field.pivot && field.pivot.is_required)) && !value) {
      errors.push(`${field.label} là bắt buộc`);
      return errors;
    }
    
    if (!value) return errors; // Skip other validations if empty and not required
    
    // Type-specific validations
    switch (field.type) {
      case 'number':
        if (isNaN(value)) {
          errors.push(`${field.label} phải là số`);
        }
        break;
        
      case 'date':
        if (!isValidDate(value)) {
          errors.push(`${field.label} phải là ngày hợp lệ`);
        }
        break;
        
      case 'select':
      case 'radio':
        const options = field.custom_options || field.options || {};
        if (!Object.keys(options).includes(value)) {
          errors.push(`${field.label} có giá trị không hợp lệ`);
        }
        break;
        
      case 'checkbox':
        if (!Array.isArray(value)) {
          errors.push(`${field.label} phải là mảng`);
        }
        break;
    }
    
    // Custom validation rules
    if (field.validation_rules && Array.isArray(field.validation_rules)) {
      field.validation_rules.forEach(rule => {
        const error = validateRule(rule, value, field.label);
        if (error) errors.push(error);
      });
    }
    
    return errors;
  }

  function validateRule(rule, value, fieldLabel) {
    if (typeof rule === 'string') {
      const [ruleName, ruleValue] = rule.split(':');
      
      switch (ruleName) {
        case 'min':
          if (value.length < parseInt(ruleValue)) {
            return `${fieldLabel} phải có ít nhất ${ruleValue} ký tự`;
          }
          break;
          
        case 'max':
          if (value.length > parseInt(ruleValue)) {
            return `${fieldLabel} không được vượt quá ${ruleValue} ký tự`;
          }
          break;
          
        case 'email':
          if (!isValidEmail(value)) {
            return `${fieldLabel} phải là email hợp lệ`;
          }
          break;
          
        case 'url':
          if (!isValidUrl(value)) {
            return `${fieldLabel} phải là URL hợp lệ`;
          }
          break;
      }
    }
    
    return null;
  }

  // Utility functions
  function isValidDate(dateString) {
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date);
  }

  function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  function isValidUrl(url) {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  // Export functions for global use
  window.DynamicFields = {
    generateFieldHTML,
    validateFieldValue,
    fieldTypeConfigs
  };

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeDynamicFields);
  } else {
    initializeDynamicFields();
  }

})();
