<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\language\LanguageController;
use App\Http\Controllers\pages\HomePage;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\DocumentController;
use App\Http\Controllers\ContractTypeController;
use App\Http\Controllers\AssetFieldController;
use App\Http\Controllers\AssetTemplateController;

// Main Page Route
Route::middleware(['auth'])->group(function () {
  // locale
  Route::get('/lang/{locale}', [LanguageController::class, 'swap']);
  // home
  Route::get('/', [HomePage::class, 'index'])->name('pages-home');

  // Admin Routes
  Route::prefix('admin')->name('admin.')->group(function () {
    // User Management
    Route::resource('users', UserController::class);
    Route::get('users-data', [UserController::class, 'getData'])->name('users.data');
    Route::post('users/{user}/reset-password', [UserController::class, 'resetPassword'])->name('users.reset-password');

    // Role Management
    Route::resource('roles', RoleController::class);
    Route::get('roles-data', [RoleController::class, 'getData'])->name('roles.data');
  });

  // Documents Management Routes
  Route::prefix('documents')->name('documents.')->group(function () {
    Route::get('/', [DocumentController::class, 'index'])->name('index');
    Route::get('/wizard', [DocumentController::class, 'wizard'])->name('wizard');
    Route::post('/store-from-wizard', [DocumentController::class, 'storeFromWizard'])->name('store-from-wizard');
    Route::get('/{document}', [DocumentController::class, 'show'])->name('show');
    Route::delete('/{document}', [DocumentController::class, 'destroy'])->name('destroy');

    // AJAX routes
    Route::get('/ajax/templates-by-contract-type', [DocumentController::class, 'getTemplatesByContractType'])->name('ajax.templates-by-contract-type');
    Route::get('/ajax/template-fields', [DocumentController::class, 'getTemplateFields'])->name('ajax.template-fields');
    Route::get('/ajax/search-parties', [DocumentController::class, 'searchParties'])->name('ajax.search-parties');
    Route::get('/ajax/search-assets', [DocumentController::class, 'searchAssets'])->name('ajax.search-assets');
  });

  // Contract Types Management
  Route::resource('contract-types', ContractTypeController::class);

  // Asset Fields Management
  Route::resource('asset-fields', AssetFieldController::class);

  // Asset Templates Management
  Route::resource('asset-templates', AssetTemplateController::class);
});
